import json
from typing import Any, Dict, List

from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from services.google.google_drive_service import GoogleDriveService
from database import get_customer_db
from loguru import logger
from controllers.customer.datametastore_queue_controller import DataMetastoreQueueController
from sqlalchemy.ext.asyncio import AsyncSession

class DataMetastoreQueueSchedulerService:
    """
    Scheduler service for processing DataMetastoreQueue items.
    """
    def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        self.is_enabled = True

    async def process_datametastore_queue(self):
        """Process new datametastore queue items"""
        if not self.is_enabled:
            logger.info("Datametastore queue scheduler is disabled, skipping processing")
            return
        try:
            async for db in get_customer_db():
                items = await DataMetastoreQueueController.get_new_items(db, limit=5)
                if not items:
                    logger.info("No new datametastore queue items found")
                    return
                logger.info(f"Processing {len(items)} new datametastore queue items")
                for item in items:
                    try:
                        job_id = str(item.job_id)
                        # Update status to PROCESSING
                        await DataMetastoreQueueController.update_status(db, job_id, "PROCESSING")
                        # Process the item
                        await self._process_datametastore_item(db, item)
                        # Update status to COMPLETED
                        await DataMetastoreQueueController.update_status(db, job_id, "COMPLETED")
                        logger.info(f"Successfully processed datametastore queue item {item.job_id}")
                    except Exception as e:
                        logger.error(f"Error processing datametastore queue item {item.job_id}: {e}")
                        # Update status to FAILED
                        await DataMetastoreQueueController.update_status(db, job_id, "FAILED")
        except Exception as e:
            logger.error(f"Error in process_datametastore_queue: {e}")

    async def _process_datametastore_item(self, db: AsyncSession, item):
        """Process a single datametastore queue item"""
        logger.info(f"Processing datametastore item: {item.job_id}")
        job_instruction = item.job_instruction
        tenant_id = item.tenant_id
        source = item.source or "LOCAL"
        if source.upper() == "LOCAL":
            await self._process_local_document(job_instruction, tenant_id)
        elif source.upper() == "GOOGLE_DRIVE":
            await self._process_google_drive_document(job_instruction, tenant_id)
        elif source.upper() == "SHAREPOINT":
            await self._process_sharepoint_document(job_instruction, tenant_id)
        else:
            logger.warning(f"Unknown source '{source}' for datametastore item {item.job_id}")

    async def _process_local_document(self, job_instruction: str, tenant_id: str):
        """Process a local document job"""
        logger.info(f"Processing local document for tenant {tenant_id} with instruction: {job_instruction}")
        # Implement your local document processing logic here
        pass

    async def _process_google_drive_document(self, job_instruction: str, tenant_id: str):
        """Process a Google Drive document job"""
        logger.info(f"Processing Google Drive document for tenant {tenant_id} with instruction: {job_instruction}")
        try:
            # Parse job_instruction (assume it's a JSON string)
            job_data = json.loads(job_instruction)
            document_id = job_data.get("documentId")
            access_token = job_data.get("accessToken")
            refresh_token = job_data.get("refreshToken")
            mime_type = job_data.get("mimeType")

            if not all([document_id, access_token, mime_type]):
                logger.error("Missing required Google Drive job parameters.")
                return

            # Get file content
            drive_service = GoogleDriveService()
            file_content = drive_service.get_file_content(
                access_token=access_token,
                file_id=document_id,
                mime_type=mime_type,
                refresh_token=refresh_token
            )

            # Do something with file_content (e.g., process, store, etc.)
            logger.info(f"Fetched content from Google Drive file {document_id} (length: {len(file_content)})")
            # ... your processing logic here ...

        except Exception as e:
            logger.error(f"Error processing Google Drive document: {e}")

    async def _process_sharepoint_document(self, job_instruction: str, tenant_id: str):
        """Process a SharePoint document job"""
        logger.info(f"Processing SharePoint document for tenant {tenant_id} with instruction: {job_instruction}")
        # Implement your SharePoint document processing logic here
        pass

    def enable(self):
        """Enable the datametastore queue scheduler (allows jobs to run)"""
        self.is_enabled = True
        logger.info("Datametastore queue scheduler enabled")

    def disable(self):
        """Disable the datametastore queue scheduler (prevents jobs from running)"""
        self.is_enabled = False
        logger.info("Datametastore queue scheduler disabled")

    def is_scheduler_enabled(self) -> bool:
        """Check if datametastore queue scheduler is enabled"""
        return self.is_enabled

    def start(self, interval_seconds: int = 30):
        """Start the datametastore queue scheduler"""
        if self.is_running:
            logger.warning("Datametastore queue scheduler is already running")
            return
        self.scheduler.add_job(
            self.process_datametastore_queue,
            IntervalTrigger(seconds=interval_seconds),
            id="process_datametastore_queue",
            name="Process Datametastore Queue"
        )
        self.scheduler.start()
        self.is_running = True
        logger.info(f"Datametastore queue scheduler started with {interval_seconds} second interval")

    def stop(self):
        """Stop the datametastore queue scheduler"""
        if not self.is_running:
            logger.warning("Datametastore queue scheduler is not running")
            return
        self.scheduler.shutdown()
        self.is_running = False
        logger.info("Datametastore queue scheduler stopped")

    def restart(self, interval_seconds: int = 30):
        """Restart the datametastore queue scheduler"""
        logger.info("Restarting datametastore queue scheduler...")
        self.stop()
        self.start(interval_seconds)

    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive datametastore queue scheduler status"""
        return {
            "is_running": self.is_running,
            "is_enabled": self.is_enabled,
            "jobs": self.get_jobs()
        }

    def get_jobs(self) -> List[Dict[str, Any]]:
        """Get information about scheduled jobs"""
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                "id": job.id,
                "name": job.name,
                "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                "trigger": str(job.trigger)
            })
        return jobs 