import docx
import PyPDF2
from io import BytesIO
from PIL import Image
from datetime import datetime
from typing import Any, Dict, List

from loguru import logger
from utils.llm import KontratarLLM
from langchain_ollama import ChatOllama

from database import get_customer_db
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from controllers.customer.datametastore_controller import DataMetastoreController
from controllers.customer.datametastore_image_controller import DatametastoreImagesController
from services.llm.ollama_llm import OllamaLLMService

llm = KontratarLLM(api_url="http://ai.kontratar.com:11434")
class DataMetastoreSchedulerService:
     """
     Scheduler service for processing DataMetastoreQueue items.
     """
     def __init__(self):
        self.scheduler = AsyncIOScheduler()
        self.is_running = False
        self.is_enabled = True     
        self.ollama_llm = OllamaLLMService()
     # Get PARTNER from datametastore, check if document id already exists in the new table
     # use created_date DESC, find a way to store the last created_date stored
     # Exttract image if pdf, or word
     # Get description throough LLM
     # Store image file, and description in table
     
     # Download the original document from the DataMetastore
     async def download_document(self, item):
          """
          Download the original document from the DataMetastore
          
          Args:
               item (DataMetastore): The document item to download
          
          Returns:
               bytes: The downloaded document bytes
          """
          try:
               # The document is already in bytes from the original_document field
               document_bytes = item.original_document
               
               if document_bytes is None:
                    logger.warning(f"No document bytes found for item {item.id}")
                    return None
               
               logger.info(f"Successfully downloaded document for item {item.id}")
               return document_bytes
          
          except Exception as e:
               logger.error(f"Error downloading document for item {item.id}: {e}")
               return None
     
     async def process_datametastore_partner_docs(self):
          """Process new datametastore partner docs items"""
          if not self.is_enabled:
               logger.info("Datametastore scheduler is disabled, skipping processing")
               return
          try:
               async for db in get_customer_db():
                    items = await DataMetastoreController.get_partner_doc_by_date(db, last_processed_date=datetime(2025, 7, 2, 9, 12, 0))
                    if not items:
                         logger.info("No new Datametastore items found")
                         return
                    logger.info(f"Processing {len(items)} new Datametastore items")
                    for item in items:
                         try:
                              created_date = item.created_date                # Process the item
                              await self._process_item(created_date, item)
                         except Exception as e:
                              logger.error(f"Error processing Datametastore item {item.job_id}: {e}")
          except Exception as e:
            logger.error(f"Error in process_datametastore_queue: {e}")

     async def _process_item(self, created_date: str, item):
        """Process a local document by extracting images"""
        
        doc_id = item.id
        identifier = item.original_document_content_type
        last_processed = item.created_date
        

        logger.info(f"Processing document with content type: {item.original_document_content_type}")
        
        # Convert byte array to file-like object
        document_bytes = BytesIO(item.original_document)
        
        # List to store extracted images
        extracted_images = []
        
        self.download_document(item)
        try:
            # Handle PDF documents
            if item.original_document_content_type == 'application/pdf':
                try:
                    pdf_reader = PyPDF2.PdfReader(document_bytes)
                    for page_num in range(len(pdf_reader.pages)):
                        page = pdf_reader.pages[page_num]
                        try:
                            for image_file_obj in page.images:
                                img = Image.open(BytesIO(image_file_obj.data))
                                extracted_images.append(img)
                        except Exception as image_error:
                            logger.warning(f"Error extracting image from page {page_num}: {image_error}")
                except Exception as pdf_error:
                    logger.warning(f"Error parsing PDF document: {pdf_error}")
                    return []
            
            # Handle Word documents
            elif item.original_document_content_type == 'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
                try:
                    doc = docx.Document(document_bytes)
                    for rel in doc.part.rels:
                        if "image" in rel.target_ref:
                            image_part = rel.target_part
                            img = Image.open(BytesIO(image_part.blob))
                            extracted_images.append(img)
                except Exception as docx_error:
                    logger.warning(f"Error parsing Word document: {docx_error}")
                    return []
            
            logger.info(f"Extracted {len(extracted_images)} images from document")
                
            # Save extracted images to database using DatametastoreImagesController
            if len(extracted_images) < 1:
                logger.info(f"No images to use")
                return []
                 
            async with get_customer_db() as db:
                for idx, img in enumerate(extracted_images):
                    # Convert image to bytes
                    img_byte_arr = BytesIO()
                    img.save(img_byte_arr, format=img.format)
                    img_bytes = img_byte_arr.getvalue()
                    
                    # Create description with document details
                    description = f"Image {idx+1} from document {doc_id}, content type: {identifier}"
                    
                    try:
                        # Generate image description using Ollama LLM
                        user_prompt = f"Describe the contents of this image from document {doc_id}"
                        description = await self.ollama_llm.prompt(user_prompt, img_bytes)
                    except Exception as llm_error:
                        logger.warning(f"Error generating image description: {llm_error}")
                    
                    # Add image to database
                    await DatametastoreImagesController.add(
                        db, 
                        document_id=str(doc_id),
                        raw_image=img_bytes,
                        description=description,
                        record_type=identifier,
                        last_processed_date=last_processed
                    )
                
                logger.info(f"Saved {len(extracted_images)} images to database for document {doc_id}")
        
        except Exception as e:
            logger.warning(f"Unexpected error processing document: {e}")
            return []
            
        return extracted_images
   
     def enable(self):
          """Enable the Datametastore scheduler (allows jobs to run)"""
          self.is_enabled = True
          logger.info("Datametastore scheduler enabled")

     def disable(self):
          """Disable the Datametastore scheduler (prevents jobs from running)"""
          self.is_enabled = False
          logger.info("Datametastore scheduler disabled")

     def is_scheduler_enabled(self) -> bool:
          """Check if Datametastore scheduler is enabled"""
          return self.is_enabled

     def start(self, interval_seconds: int = 30):
          """Start the datametastore scheduler"""
          if self.is_running:
               logger.warning("Datametastore scheduler is already running")
               return
          self.scheduler.add_job(
               self.process_datametastore_partner_docs,
               IntervalTrigger(seconds=interval_seconds),
               id="process_datametastore_queue",
               name="Process Datametastore Queue"
          )
          self.scheduler.start()
          self.is_running = True
          logger.info(f"Datametastore scheduler started with {interval_seconds} second interval")

     def stop(self):
          """Stop the Datametastore scheduler"""
          if not self.is_running:
               logger.warning("Datametastore scheduler is not running")
               return
          self.scheduler.shutdown()
          self.is_running = False
          logger.info("Datametastore scheduler stopped")

     def restart(self, interval_seconds: int = 30):
          """Restart the Datametastore scheduler"""
          logger.info("Restarting Datametastore scheduler...")
          self.stop()
          self.start(interval_seconds)

     def get_status(self) -> Dict[str, Any]:
          """Get comprehensive Datametastore scheduler status"""
          return {
               "is_running": self.is_running,
               "is_enabled": self.is_enabled,
               "jobs": self.get_jobs()
          }

     def get_jobs(self) -> List[Dict[str, Any]]:
          """Get information about scheduled jobs"""
          jobs = []
          for job in self.scheduler.get_jobs():
               jobs.append({
                    "id": job.id,
                    "name": job.name,
                    "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                    "trigger": str(job.trigger)
               })
          return jobs 