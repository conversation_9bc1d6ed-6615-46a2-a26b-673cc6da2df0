from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.customer_models import CustomOppsQueue as CustomerCustomOppsQueue
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession


class CustomOppsQueueService:
    """Service for handling custom opportunities queue operations"""
    
    @staticmethod
    async def get_custom_opps_queue_items(db: AsyncSession, limit: int = 10) -> List[CustomerCustomOppsQueue]:
        """Get new custom opps queue items"""
        try:
            query = select(CustomerCustomOppsQueue).where(
                CustomerCustomOppsQueue.status == "COMPLETED"
            ).order_by(CustomerCustomOppsQueue.created_date.asc()).limit(limit)
            
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting new custom opps queue items: {e}")
            return []
    
    @staticmethod
    async def update_custom_opps_queue_status(
        db: AsyncSession, 
        opps_id: str, 
        status: str
    ) -> bool:
        """Update custom opps queue status"""
        try:
            query = update(CustomerCustomOppsQueue).where(
                CustomerCustomOppsQueue.opps_id == opps_id
            ).values(
                status=status,
                update_date=datetime.utcnow()
            )
            
            result = await db.execute(query)
            await db.commit()
            
            logger.info(f"Updated custom opps queue status for opps_id {opps_id} to {status}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating custom opps queue status: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def create_custom_opps_queue_item(
        db: AsyncSession,
        opps_source: str,
        opps_id: str,
        tenant_id: str,
        originating_ip_address: Optional[str] = None
    ) -> Optional[CustomerCustomOppsQueue]:
        """Create a new custom opps queue item"""
        try:
            new_item = CustomerCustomOppsQueue(
                opps_source=opps_source,
                opps_id=opps_id,
                tenant_id=tenant_id,
                status="NEW",
                originating_ip_address=originating_ip_address,
                created_date=datetime.utcnow()
            )
            
            db.add(new_item)
            await db.commit()
            await db.refresh(new_item)
            
            logger.info(f"Created new custom opps queue item for opps_id {opps_id}")
            return new_item
        except Exception as e:
            logger.error(f"Error creating custom opps queue item: {e}")
            await db.rollback()
            return None 