import re
from typing import Any, Dict, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.kontratar.ebuy_opps_controller import EBUYOppsController
from controllers.kontratar.opps_table_controller import OppsTableController
from services.chroma.chroma_service import ChromaService

from database import get_customer_db, get_kontratar_db
from langchain_ollama import ChatOllama
from services.proposal.utilities import ProposalUtilities
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type

from loguru import logger

def remove_first_markdown_title_regex(text: str) -> str:
    """
    Remove only the first line that starts with ## using regex.
    
    Args:
        text: Input text containing markdown titles
        
    Returns:
        Text with only the first ## title removed
    """
    # Remove only the first line starting with ## (with optional whitespace before ##)
    return re.sub(r'^\s*##.*$', '', text, flags=re.MULTILINE, count=1).strip()


class ProposalOutlineService:
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        self.sam_service = OppsTableController()
        self.ebuy_service = EBUYOppsController()
        self.custom_service = CustomOpportunitiesController()

        self.chroma_service = ChromaService(embedding_api_url, None)
        #self.llm = KontratarLLM(api_url=llm_api_url, api_key=None)
        self.llm = ChatOllama(
            model="gemma3:27b", 
            num_ctx=6300, 
            temperature=0, 
            base_url=llm_api_url
        )

    def generate_chroma_query(self, text: str, is_rfp: bool = True):
        if not text:
            return ""

        llm = ChatOllama(model="gemma3:27b", base_url="http://ai.kontratar.com:11434")
        if is_rfp:
            prompt = (
                "Given the following RFP volume information, generate a concise search query that would retrieve the most relevant government opportunity information from a vector database collection. "
                "The query should focus on requirements, evaluation criteria, and any details relevant to the specific volume described below. "
                "This is for a single volume of a multi-volume RFP.\n\n"
                f"{text}\n\n"
                "Search Query:"
            )
        else:
            prompt = (
                "Given the following RFI information, generate a concise search query that would retrieve the most relevant government opportunity information from a vector database collection. "
                "The query should focus on requirements, government needs, and any details relevant to the RFI topics described below.\n\n"
                f"{text}\n\n"
                "Search Query:"
            )
        response = llm.invoke(prompt)
        return str(response.content)

    async def get_opportunity(self, opportunity_id: str, tenant_id: str, source: str):
        logger.info(f"get_opportunity called with opportunity_id={opportunity_id}, tenant_id={tenant_id}, source={source}")
        record = None
        if source == "sam":
            logger.info(f"Searching SAM for notice_id={opportunity_id}")
            async for db in get_kontratar_db():
                record = await self.sam_service.get_by_notice_id(db, opportunity_id)
                logger.info(f"SAM search result: {record}")
                break
        elif source == "ebuy":
            logger.info(f"Searching EBUY for rfq_id={opportunity_id}")
            async for db in get_kontratar_db():
                record = await self.ebuy_service.get_by_rfq_id(db, opportunity_id)
                logger.info(f"EBUY search result: {record}")
                break
        elif source == "custom":
            logger.info(f"Searching CUSTOM for opportunity_id={opportunity_id}")
            async for db in get_customer_db():
                record = await self.custom_service.get_main_info_by_opportunity_id(db, opportunity_id)
                logger.info(f"CUSTOM search result: {record}")
                break
        else:
            logger.error(f"Invalid source type: {source}")
            raise ValueError("Invalid source type")

        if record is None:
            logger.error(f"Error getting opportunity metadata for id={opportunity_id}, source={source}")
            raise ValueError("Error getting opportunity metadata")

        logger.info(f"Returning opportunity record: {record}")
        return record        

    async def generate_table_of_contents(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        volume_information: str,
        content_compliance: str,
        is_rfp: bool
    ) -> Dict[str, Any]:
        '''
        chroma_query = self.generate_chroma_query(volume_information, is_rfp)

        async for db in get_kontratar_db():
            max_chunks = 1
            collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
            relevant_chunks = await self.chroma_service.get_relevant_chunks(
                db, collection_name, chroma_query, n_results=max_chunks
            )
            # Clean up newlines and tabs
            toc_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
            context = "\n".join(toc_context)
            break
        '''

        system_prompt = '''
            **Task:**
            Your task is to generate a table of contents for an RFI or Volume of an RFP.
            You will be given different background information to help you generate this table of contents:
            1. Information on how the RFI or Volume should be structured as well as the terminologies/naming convention to use, 
            this will be found in <structure-compliance>
            2. Information on the content that must been to be seen in the RFI or Volume, this will be found in <content-compliance>
            3. Related information from the vector database, this will be found in <context>
        
            **Important:**
            1. YOU MUST only generate the table of contents for the RFI or RFP Volume passed in <structure-compliance>
            2. If you are building the table of contents for an RFP Volume, only use information relevant information found in <content-compliance>
            3. YOU MUST ADD 3 experiences UNDER DEMONSTRATED EXPERIENCE or PAST PERFORMANCE.
            4. YOU MUST ADD ALL Statement of Work Tasks to the TECHNICAL APPROACH (or similar) with the exact naming. DO NOT leave out ANYTHING from the SOW.
            5. The response SHOULD be returned as JSON so ensure to generate accurate, parsebale and valid JSON.
            6. You will be given a JSON schema to comply to, ensure to follow it strictly.
        '''

        user_prompt = f'''
            Generate the table of contents for this RFI/RFP
            
            <structure-compliance>
                {volume_information}
            </structure-compliance>

            <content-compliance>
                {content_compliance}
            </content-compliance>

            USE ONLY information found in <structure-compliance>, <content-compliance>, and <context> to build the response.

            Use the JSON schema below:
            {{
                "table_of_contents": [
                    {{
                        "title": "string",
                        "description": "string",
                        "number": "string",
                        "subsections": [
                            {{
                                "number": "string",
                                "title": "string",
                                "description": "string"
                            }}
                        ]
                    }}
                ]
            }}

            - "table_of_contents" is an array of sections for the RFI or RFP Volume.
            - "title" is the name of the main section or subsection AND it MUST align with the naming conventions used in <structure-compliance>
            - "description" MUST be a brief description of the content that must be contained in that section or subsection.
            - "subsections" is an array of subsections for each section.
            - "number" is THE assigned number for a main section or subsection (eg main section 1.0, subsection 1.1.3). "number" MUST
                start from 1.0
        '''

        # LLM call (with retry logic)
        content = None
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        content = self.llm.invoke(messages)

        print(content.content)
        
        return {"content": content.content}

    
    async def generate_outline(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate a detailed outline for each section and subsection in the table of contents.
        Each outline contains a title, content, and optionally an array of image descriptions.
        The outlines are nested to match the table of contents hierarchy.
        """

        @retry(
            stop=stop_after_attempt(5),  # Retry up to 3 times
            wait=wait_fixed(3),          # Wait 1 second between retries
            retry=retry_if_exception_type(Exception),  # Retry on any Exception
            reraise=True                 # Reraise the last exception if all retries fail
        )
        async def outline_for_section(section: Dict[str, Any]) -> Dict[str, Any]:
            # Compose a query for this section
            section_title = section.get("title", "")
            section_desc = section.get("description", "")

            logger.info(f"Section title: {section_title}, Section Description: {section_desc}")
            
            chroma_query = f"""
                Return all the content needed to be seen in proposal section titled '{section_title}'.
            """

            # Fetch relevant context for this section
            async for db in get_kontratar_db():
                collection_name = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
                relevant_chunks = await self.chroma_service.get_relevant_chunks(
                    db, collection_name, chroma_query, n_results=3
                )
                section_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
                context = "\n".join(section_context)
                #logger.info(f"Context: {section_context}")
                break

            system_prompt = '''
                **Task:**
                For the given proposal section, generate a detailed outline.
                The outline should include:
                - A title (from the section/subsection)
                - Content: a detailed guide on what to include, and what NOT to include if relevant, for that section/subsection.
                - Page limit: the max number of pages allowed for this section
                - References: References from <context> used to generate the section of this outline. Whatver reference you include, 
                show the full text of it.
                - Purpose: The purpose of this section eg. To Persuade, To Identify, to Justify
                - Opportunity vector DB query: A detailed vector database query that will get information about the government 
                opportunity relevant to this section.
                - Client Vector DB Query: A detailed vector database query to get information about the client relevant to respond to this section.
                - Custom Prompt: A very specific, customized and in-depth prompt to successfully generate 
                content for this section. It should also contain step by step instructins on what to do. Using your knowledge, if there 
                are any tables or diagrams that MUST be included, please say so as well.
                - Optionally, an array of image_descriptions if an image or diagram is necessary for that section/subsection.

                **Important:**
                1. Use only information found in <context> and the section/subsection title/description to build the outline.
                2. Custom Prompt must include the usage of naming conventions found in RFP/RFI.
                3. ANY table that must be created MUST be included in the "image_descriptions" array.
                4. The response for each section/subsection MUST be valid JSON and follow the schema strictly.
                5. Do not invent content; base your outline on best practices and the provided context.
                6. The response SHOULD be returned as JSON so ensure to generate accurate, parsebale and valid JSON.
                7. You will be given a JSON schema to comply to, ensure to follow it strictly.

                **Special Sections Instructions (Regardless of Volume):**
                1. Staffing Plan sections MUST always have a table (Columns: Role, Responsibilities, Qualifications)
                2. Technical Approach should always include the SOW tasks and how they would be approached
                3. Pricing sections MAY or MAY not need a table
                
            '''

            user_prompt = f'''
                <section-title>
                    {section_title}
                </section-title>

                <section-description>
                    {section_desc}
                </section-description>

                <context>
                    {context}
                </context>

                Use the following JSON schema for your response:
                {{
                    "title": "string",
                    "content": "string",
                    "page_limit": number,
                    "purpose": "string",
                    "rfp_vector_db_query": "string",
                    "client_vector_db_query": "string",
                    "custom_prompt": "string",
                    "references": "string",
                    "image_descriptions": ["string"]  // Only include this field if an image is necessary
                }}
            '''

            # LLM call with retry logic
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            result = self.llm.invoke(messages)
            content = str(result.content)
            
            # Parse the LLM output (assume it's valid JSON)
            outline = ProposalUtilities.extract_json_from_brackets(content)

            if outline is None:
                return {}
            
            print(f"Raw content: {content}, Processed Content: {outline}")

            # Recursively process subsections
            subsections = section.get("subsections", [])
            if subsections:
                outline["subsections"] = []
                for subsection in subsections:
                    sub_outline = await outline_for_section(subsection)
                    outline["subsections"].append(sub_outline)

            return outline

        # Build the nested outline structure
        outlines = []
        for section in table_of_contents:
            outline = await outline_for_section(section)
            outlines.append(outline)

        return {"outlines": outlines}


    ## This is to generate a draft
    async def generate_draft(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: str,
        tenant_metadata: str,
        table_of_contents: List[Dict[str, Any]],
    ) -> Dict[str, Any]:
        """
        Generate a draft for each section and subsection in the table of contents.
        The drafts are nested to match the table of contents hierarchy.
        """

        record = await self.get_opportunity(opportunity_id, tenant_id, source)

        @retry(
            stop=stop_after_attempt(5),  # Retry up to 3 times
            wait=wait_fixed(3),          # Wait 1 second between retries
            retry=retry_if_exception_type(Exception),  # Retry on any Exception
            reraise=True                 # Reraise the last exception if all retries fail
        )
        async def draft_for_section(section: Dict[str, Any]) -> Dict[str, Any]:
            # Compose a query for this section
            section_title = section.get("title", "")
            section_desc = section.get("description", "")
            section_number = section.get("number", "")

            logger.info(f"Section title: {section_title}, Section Description: {section_desc}")
            
            # Fetch relevant context for this section
            
            chroma_query = f"""
                Return all the content needed to be seen in proposal section titled '{section_title}'.
            """

            client_query = f"""
                Return information relevant to a proposal section titled '{section_title}'
            """

            async for db in get_kontratar_db():
                opportunity_collection = f"{tenant_id}_{opportunity_id}" if source.lower() == "custom" else opportunity_id
                opportunity_chunks = await self.chroma_service.get_relevant_chunks(
                    db, opportunity_collection, chroma_query, n_results=3
                )
                rfp_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in opportunity_chunks]
                context = "\n".join(rfp_context)
                #logger.info(f"Context: {section_context}")

                client_collection = f"{tenant_id}_{client_short_name}"
                relevant_chunks = await self.chroma_service.get_relevant_chunks(
                    db, client_collection, client_query, n_results=3
                )
                client_context = [chunk.replace("\n", " ").replace("\t", " ") for chunk in relevant_chunks]
                client_context_str = "\n".join(client_context)

                break
            

            system_prompt = '''
                **Task:**
                For the given proposal section, generate the content for that section.
                You are free to generate relevant tables BUT YOU MUST generate them in markdown.
                ONLY generate tables when they will be relevant. The tables MUST NOT be incomplete.

                You will be given relevant infromation from the RFP's attachments as well as the company information 
                that you can use to generate the RFP section. ENSURE to tailor it using that information and same naming conventions.
            '''

            user_prompt = f'''
                Write a {section_title} for an RFP. Return only the section, DO NOT return anything 
                else before or after it. DO NOT leave placeholders anywhere. DO NOT ADD the section title at the beginning

                ## Information from RFP Attachments:
                {record}
                {context}

                ## Company Information:
                {tenant_metadata}
                {client_context_str}
            '''

            # LLM call with retry logic
            messages = [
                ("system", system_prompt),
                ("human", user_prompt)
            ]
            result = self.llm.invoke(messages)
            content = str(result.content)
            
            # Parse the LLM output (assume it's valid JSON)
            #outline = ProposalUtilities.extract_json_from_brackets(content)
            text = remove_first_markdown_title_regex(content)

            draft = { 
                "title": section_number + " " + section_title, 
                "content": text, 
                "number": section_number 
            }
            
            print(f"Raw content: {text}")

            # Recursively process subsections
            subsections = section.get("subsections", [])
            if subsections:
                draft["subsections"] = []
                for subsection in subsections:
                    sub_outline = await draft_for_section(subsection)
                    draft["subsections"].append(sub_outline)

            return draft

        # Build the nested outline structure
        drafts = []
        for section in table_of_contents:
            outline = await draft_for_section(section)
            drafts.append(outline)

        return { "draft": drafts }

    

