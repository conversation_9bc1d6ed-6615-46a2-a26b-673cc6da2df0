"""
Database Integration for Multi-Agent Proposal System.
Ensures compatibility with existing database storage format for PDF export functionality.
"""

import json
import asyncio
from typing import Any, Dict, List, Optional
from datetime import datetime

from loguru import logger

from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.kontratar.ebuy_opps_controller import EBUYOppsController
from controllers.kontratar.opps_table_controller import OppsTableController
from database import get_customer_db, get_kontratar_db


class MultiAgentDatabaseIntegration:
    """
    Database integration service for multi-agent proposal system.
    Handles storing proposal data in the same format as the legacy system.
    """
    
    def __init__(self):
        self.custom_controller = CustomOpportunitiesController
        self.ebuy_controller = EBUYOppsController
        self.sam_controller = OppsTableController
        
        logger.info("Multi-Agent Database Integration initialized")
    
    async def store_proposal_data(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        table_of_contents: List[Dict[str, Any]],
        proposal_draft: Dict[str, Any],
        additional_data: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Store proposal data in the database using the same format as legacy system.
        
        Args:
            opportunity_id: The opportunity identifier
            tenant_id: The tenant identifier
            source: The opportunity source (sam, ebuy, custom)
            table_of_contents: The table of contents structure
            proposal_draft: The generated proposal draft
            additional_data: Additional data to store (optional)
            
        Returns:
            bool: True if successful, False otherwise
        """
        
        try:
            logger.info(f"Storing proposal data for opportunity {opportunity_id} (source: {source})")
            
            # Prepare data for storage
            toc_json = json.dumps(table_of_contents)
            draft_json = json.dumps(proposal_draft.get("draft", []))
            
            # Prepare update fields
            update_fields = {
                "toc_text": toc_json,
                "draft": draft_json
            }
            
            # Add additional metadata if provided
            if additional_data:
                # Store quality and compliance scores if available
                metadata = additional_data.get("metadata", {})
                if "quality_score" in metadata:
                    update_fields["quality_score"] = metadata["quality_score"]
                if "compliance_score" in metadata:
                    update_fields["compliance_score"] = metadata["compliance_score"]
                
                # Store generation timestamp
                if "generated_at" in metadata:
                    update_fields["last_generation_date"] = metadata["generated_at"]
            
            # Store based on source
            success = await self._store_by_source(
                source=source,
                opportunity_id=opportunity_id,
                update_fields=update_fields
            )
            
            if success:
                logger.info(f"Successfully stored proposal data for opportunity {opportunity_id}")
            else:
                logger.error(f"Failed to store proposal data for opportunity {opportunity_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error storing proposal data for opportunity {opportunity_id}: {e}")
            return False
    
    async def _store_by_source(
        self,
        source: str,
        opportunity_id: str,
        update_fields: Dict[str, Any]
    ) -> bool:
        """
        Store data based on the opportunity source.
        """
        
        source = source.lower()
        
        try:
            if source == "custom":
                return await self._store_custom_opportunity(opportunity_id, update_fields)
            elif source == "ebuy":
                return await self._store_ebuy_opportunity(opportunity_id, update_fields)
            elif source == "sam":
                return await self._store_sam_opportunity(opportunity_id, update_fields)
            else:
                logger.error(f"Unknown opportunity source: {source}")
                return False
                
        except Exception as e:
            logger.error(f"Error storing data for source {source}: {e}")
            return False
    
    async def _store_custom_opportunity(
        self,
        opportunity_id: str,
        update_fields: Dict[str, Any]
    ) -> bool:
        """
        Store data for custom opportunities.
        """
        
        try:
            async for db in get_customer_db():
                updated_record = await self.custom_controller.update_by_opportunity_id(
                    db=db,
                    opportunity_id=opportunity_id,
                    update_fields=update_fields
                )
                break
            
            return updated_record is not None
            
        except Exception as e:
            logger.error(f"Error storing custom opportunity data: {e}")
            return False
    
    async def _store_ebuy_opportunity(
        self,
        opportunity_id: str,
        update_fields: Dict[str, Any]
    ) -> bool:
        """
        Store data for eBuy opportunities.
        """
        
        try:
            async for db in get_kontratar_db():
                # Map fields to eBuy table structure
                ebuy_fields = {}
                if "toc_text" in update_fields:
                    ebuy_fields["toc_text"] = update_fields["toc_text"]
                if "draft" in update_fields:
                    ebuy_fields["proposal_outline"] = update_fields["draft"]
                
                updated_record = await self.ebuy_controller.update_by_rfq_id(
                    db=db,
                    rfq_id=opportunity_id,
                    update_fields=ebuy_fields
                )
                break
            
            return updated_record is not None
            
        except Exception as e:
            logger.error(f"Error storing eBuy opportunity data: {e}")
            return False
    
    async def _store_sam_opportunity(
        self,
        opportunity_id: str,
        update_fields: Dict[str, Any]
    ) -> bool:
        """
        Store data for SAM opportunities.
        """
        
        try:
            async for db in get_kontratar_db():
                # Map fields to SAM table structure
                sam_fields = {}
                if "toc_text" in update_fields:
                    sam_fields["toc_text"] = update_fields["toc_text"]
                if "draft" in update_fields:
                    sam_fields["proposal_outline"] = update_fields["draft"]
                
                updated_record = await self.sam_controller.update_by_notice_id(
                    db=db,
                    notice_id=opportunity_id,
                    update_fields=sam_fields
                )
                break
            
            return updated_record is not None
            
        except Exception as e:
            logger.error(f"Error storing SAM opportunity data: {e}")
            return False
    
    async def retrieve_proposal_data(
        self,
        opportunity_id: str,
        source: str
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve existing proposal data from the database.
        
        Args:
            opportunity_id: The opportunity identifier
            source: The opportunity source (sam, ebuy, custom)
            
        Returns:
            Dict containing existing proposal data or None if not found
        """
        
        try:
            source = source.lower()
            
            if source == "custom":
                return await self._retrieve_custom_opportunity(opportunity_id)
            elif source == "ebuy":
                return await self._retrieve_ebuy_opportunity(opportunity_id)
            elif source == "sam":
                return await self._retrieve_sam_opportunity(opportunity_id)
            else:
                logger.error(f"Unknown opportunity source: {source}")
                return None
                
        except Exception as e:
            logger.error(f"Error retrieving proposal data for opportunity {opportunity_id}: {e}")
            return None
    
    async def _retrieve_custom_opportunity(
        self,
        opportunity_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve data for custom opportunities.
        """
        
        try:
            async for db in get_customer_db():
                record = await self.custom_controller.get_by_opportunity_id(
                    db=db,
                    opportunity_id=opportunity_id
                )
                break
            
            if not record:
                return None
            
            # Parse JSON data
            toc_data = None
            draft_data = None
            
            if record.toc_text:
                try:
                    toc_data = json.loads(record.toc_text)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON in toc_text for opportunity {opportunity_id}")
            
            if record.draft:
                try:
                    draft_data = json.loads(record.draft)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON in draft for opportunity {opportunity_id}")
            
            return {
                "table_of_contents": toc_data,
                "draft": draft_data,
                "format_compliance": record.format_compliance
            }
            
        except Exception as e:
            logger.error(f"Error retrieving custom opportunity data: {e}")
            return None
    
    async def _retrieve_ebuy_opportunity(
        self,
        opportunity_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve data for eBuy opportunities.
        """
        
        try:
            async for db in get_kontratar_db():
                record = await self.ebuy_controller.get_by_rfq_id(
                    db=db,
                    rfq_id=opportunity_id
                )
                break
            
            if not record:
                return None
            
            # Parse JSON data
            toc_data = None
            draft_data = None
            
            if hasattr(record, 'toc_text') and record.toc_text:
                try:
                    toc_data = json.loads(record.toc_text)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON in toc_text for eBuy opportunity {opportunity_id}")
            
            if hasattr(record, 'proposal_outline') and record.proposal_outline:
                try:
                    draft_data = json.loads(record.proposal_outline)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON in proposal_outline for eBuy opportunity {opportunity_id}")
            
            return {
                "table_of_contents": toc_data,
                "draft": draft_data
            }
            
        except Exception as e:
            logger.error(f"Error retrieving eBuy opportunity data: {e}")
            return None
    
    async def _retrieve_sam_opportunity(
        self,
        opportunity_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve data for SAM opportunities.
        """
        
        try:
            async for db in get_kontratar_db():
                record = await self.sam_controller.get_by_notice_id(
                    db=db,
                    notice_id=opportunity_id
                )
                break
            
            if not record:
                return None
            
            # Parse JSON data
            toc_data = None
            draft_data = None
            
            if hasattr(record, 'toc_text') and record.toc_text:
                try:
                    toc_data = json.loads(record.toc_text)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON in toc_text for SAM opportunity {opportunity_id}")
            
            if hasattr(record, 'proposal_outline') and record.proposal_outline:
                try:
                    draft_data = json.loads(record.proposal_outline)
                except json.JSONDecodeError:
                    logger.warning(f"Invalid JSON in proposal_outline for SAM opportunity {opportunity_id}")
            
            return {
                "table_of_contents": toc_data,
                "draft": draft_data
            }
            
        except Exception as e:
            logger.error(f"Error retrieving SAM opportunity data: {e}")
            return None
