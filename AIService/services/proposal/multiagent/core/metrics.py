"""
Quality and Compliance Metrics for Multi-Agent Proposal Generation.
Provides comprehensive scoring and validation metrics.
"""

import re
import statistics
from datetime import datetime
from typing import Any, Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum

from loguru import logger


class MetricType(str, Enum):
    QUALITY = "quality"
    COMPLIANCE = "compliance"
    COMPLETENESS = "completeness"
    CONSISTENCY = "consistency"
    READABILITY = "readability"
    TECHNICAL_ACCURACY = "technical_accuracy"


class SeverityLevel(str, Enum):
    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


@dataclass
class MetricResult:
    metric_type: MetricType
    score: float  # 0.0 to 1.0
    max_score: float = 1.0
    details: Dict[str, Any] = None
    issues: List[str] = None
    recommendations: List[str] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}
        if self.issues is None:
            self.issues = []
        if self.recommendations is None:
            self.recommendations = []
    
    @property
    def percentage(self) -> float:
        return (self.score / self.max_score) * 100.0


@dataclass
class ValidationIssue:
    issue_id: str
    severity: SeverityLevel
    category: str
    description: str
    location: Optional[str] = None
    suggestion: Optional[str] = None
    auto_fixable: bool = False


class QualityMetrics:
    """
    Comprehensive quality assessment for proposal content.
    """
    
    def __init__(self):
        self.required_sections = {
            1: ["Executive Summary", "Technical Approach", "Management Plan", "Staffing Plan"],
            2: ["Cost Proposal", "Pricing", "Budget"],
            3: ["Past Performance", "Experience", "References"],
            4: ["Small Business Plan", "Subcontracting"],
            5: ["Certifications", "Compliance", "Security"]
        }
    
    def calculate_quality_score(self, content: str, section_title: str = "", volume_number: int = 1) -> MetricResult:
        """Calculate overall quality score for content"""
        
        scores = {}
        all_issues = []
        all_recommendations = []
        
        # Readability score
        readability = self._calculate_readability(content)
        scores['readability'] = readability.score
        all_issues.extend(readability.issues)
        all_recommendations.extend(readability.recommendations)
        
        # Completeness score
        completeness = self._calculate_completeness(content, section_title, volume_number)
        scores['completeness'] = completeness.score
        all_issues.extend(completeness.issues)
        all_recommendations.extend(completeness.recommendations)
        
        # Consistency score
        consistency = self._calculate_consistency(content)
        scores['consistency'] = consistency.score
        all_issues.extend(consistency.issues)
        all_recommendations.extend(consistency.recommendations)
        
        # Technical accuracy score
        technical = self._calculate_technical_accuracy(content)
        scores['technical'] = technical.score
        all_issues.extend(technical.issues)
        all_recommendations.extend(technical.recommendations)
        
        # Weighted average
        weights = {
            'readability': 0.2,
            'completeness': 0.3,
            'consistency': 0.25,
            'technical': 0.25
        }
        
        overall_score = sum(scores[metric] * weights[metric] for metric in scores)
        
        return MetricResult(
            metric_type=MetricType.QUALITY,
            score=overall_score,
            details=scores,
            issues=all_issues,
            recommendations=all_recommendations
        )
    
    def _calculate_readability(self, content: str) -> MetricResult:
        """Calculate readability metrics"""
        issues = []
        recommendations = []
        
        # Basic readability checks
        sentences = re.split(r'[.!?]+', content)
        words = content.split()
        
        if not sentences or not words:
            return MetricResult(
                metric_type=MetricType.READABILITY,
                score=0.0,
                issues=["Content is empty or too short"],
                recommendations=["Add substantial content"]
            )
        
        # Average sentence length
        avg_sentence_length = len(words) / len(sentences)
        
        # Word complexity (simple heuristic)
        complex_words = sum(1 for word in words if len(word) > 6)
        complexity_ratio = complex_words / len(words) if words else 0
        
        # Passive voice detection (simple heuristic)
        passive_indicators = ['was', 'were', 'been', 'being', 'is', 'are', 'am']
        passive_count = sum(1 for word in words if word.lower() in passive_indicators)
        passive_ratio = passive_count / len(words) if words else 0
        
        # Scoring
        score = 1.0
        
        if avg_sentence_length > 25:
            score -= 0.2
            issues.append("Sentences are too long (average > 25 words)")
            recommendations.append("Break down long sentences for better readability")
        
        if complexity_ratio > 0.3:
            score -= 0.2
            issues.append("Too many complex words")
            recommendations.append("Use simpler language where possible")
        
        if passive_ratio > 0.2:
            score -= 0.1
            issues.append("Excessive use of passive voice")
            recommendations.append("Use active voice for clearer communication")
        
        return MetricResult(
            metric_type=MetricType.READABILITY,
            score=max(0.0, score),
            details={
                "avg_sentence_length": avg_sentence_length,
                "complexity_ratio": complexity_ratio,
                "passive_ratio": passive_ratio,
                "word_count": len(words),
                "sentence_count": len(sentences)
            },
            issues=issues,
            recommendations=recommendations
        )
    
    def _calculate_completeness(self, content: str, section_title: str, volume_number: int) -> MetricResult:
        """Calculate completeness based on expected content"""
        issues = []
        recommendations = []
        
        # Check minimum word count
        words = content.split()
        word_count = len(words)
        
        # Expected word counts by volume
        expected_word_counts = {
            1: 500,  # Technical volume
            2: 200,  # Cost volume
            3: 300,  # Past performance
            4: 150,  # Small business
            5: 100   # Certifications
        }
        
        expected_count = expected_word_counts.get(volume_number, 300)
        
        score = 1.0
        
        if word_count < expected_count * 0.5:
            score -= 0.5
            issues.append(f"Content is too short ({word_count} words, expected ~{expected_count})")
            recommendations.append(f"Expand content to at least {expected_count} words")
        elif word_count < expected_count * 0.8:
            score -= 0.2
            issues.append(f"Content may be insufficient ({word_count} words)")
            recommendations.append("Consider adding more detail")
        
        # Check for required elements based on section
        required_elements = self._get_required_elements(section_title, volume_number)
        missing_elements = []
        
        for element in required_elements:
            if element.lower() not in content.lower():
                missing_elements.append(element)
        
        if missing_elements:
            score -= 0.3 * (len(missing_elements) / len(required_elements))
            issues.append(f"Missing required elements: {', '.join(missing_elements)}")
            recommendations.append(f"Include discussion of: {', '.join(missing_elements)}")
        
        return MetricResult(
            metric_type=MetricType.COMPLETENESS,
            score=max(0.0, score),
            details={
                "word_count": word_count,
                "expected_word_count": expected_count,
                "missing_elements": missing_elements,
                "required_elements": required_elements
            },
            issues=issues,
            recommendations=recommendations
        )
    
    def _calculate_consistency(self, content: str) -> MetricResult:
        """Calculate consistency in terminology and style"""
        issues = []
        recommendations = []
        
        # Check for consistent terminology
        # This is a simplified version - in practice, you'd have domain-specific terms
        
        score = 1.0
        
        # Check for consistent acronym usage
        acronyms = re.findall(r'\b[A-Z]{2,}\b', content)
        acronym_usage = {}
        
        for acronym in acronyms:
            if acronym in acronym_usage:
                acronym_usage[acronym] += 1
            else:
                acronym_usage[acronym] = 1
        
        # Check for inconsistent capitalization
        words = content.split()
        word_variations = {}
        
        for word in words:
            lower_word = word.lower()
            if lower_word in word_variations:
                word_variations[lower_word].add(word)
            else:
                word_variations[lower_word] = {word}
        
        inconsistent_words = [
            word for word, variations in word_variations.items()
            if len(variations) > 1 and len(word) > 3
        ]
        
        if inconsistent_words:
            score -= 0.1 * min(len(inconsistent_words) / 10, 0.5)
            issues.append(f"Inconsistent capitalization: {', '.join(inconsistent_words[:5])}")
            recommendations.append("Ensure consistent capitalization throughout")
        
        return MetricResult(
            metric_type=MetricType.CONSISTENCY,
            score=max(0.0, score),
            details={
                "acronym_count": len(acronym_usage),
                "inconsistent_words": len(inconsistent_words)
            },
            issues=issues,
            recommendations=recommendations
        )
    
    def _calculate_technical_accuracy(self, content: str) -> MetricResult:
        """Calculate technical accuracy score"""
        issues = []
        recommendations = []
        
        score = 1.0
        
        # Check for placeholder text
        placeholders = re.findall(r'\[.*?\]|\{.*?\}|TODO|TBD|PLACEHOLDER', content, re.IGNORECASE)
        
        if placeholders:
            score -= 0.4
            issues.append(f"Found {len(placeholders)} placeholders or TODO items")
            recommendations.append("Replace all placeholders with actual content")
        
        # Check for vague language
        vague_terms = ['various', 'multiple', 'several', 'many', 'some', 'appropriate', 'suitable']
        vague_count = sum(1 for term in vague_terms if term in content.lower())
        
        if vague_count > 5:
            score -= 0.2
            issues.append("Excessive use of vague language")
            recommendations.append("Use specific, quantifiable terms")
        
        return MetricResult(
            metric_type=MetricType.TECHNICAL_ACCURACY,
            score=max(0.0, score),
            details={
                "placeholder_count": len(placeholders),
                "vague_term_count": vague_count
            },
            issues=issues,
            recommendations=recommendations
        )
    
    def _get_required_elements(self, section_title: str, volume_number: int) -> List[str]:
        """Get required elements for a specific section"""
        
        # This would be expanded with comprehensive requirements
        requirements_map = {
            "executive summary": ["overview", "approach", "benefits", "qualifications"],
            "technical approach": ["methodology", "timeline", "deliverables", "risks"],
            "management plan": ["organization", "communication", "reporting", "oversight"],
            "staffing plan": ["roles", "responsibilities", "qualifications", "availability"],
            "past performance": ["contracts", "references", "outcomes", "relevance"],
            "cost proposal": ["labor", "materials", "overhead", "profit"],
            "small business plan": ["goals", "subcontractors", "outreach", "reporting"]
        }
        
        section_key = section_title.lower()
        for key, elements in requirements_map.items():
            if key in section_key:
                return elements
        
        return []


class ComplianceMetrics:
    """
    Compliance assessment for government proposal requirements.
    """
    
    def __init__(self):
        self.compliance_rules = self._load_compliance_rules()
    
    def _load_compliance_rules(self) -> Dict[str, Any]:
        """Load compliance rules and requirements"""
        # This would be loaded from a configuration file or database
        return {
            "far_compliance": {
                "required_clauses": ["52.204-24", "52.209-5", "52.212-3"],
                "prohibited_terms": ["guarantee", "warranty", "promise"],
                "required_certifications": ["CAGE", "DUNS", "SAM"]
            },
            "format_requirements": {
                "max_pages": {"volume_1": 50, "volume_2": 20, "volume_3": 30},
                "font_size": 12,
                "margins": 1.0,
                "line_spacing": "double"
            }
        }
    
    def calculate_compliance_score(
        self,
        content: str,
        requirements: List[str],
        volume_number: int = 1
    ) -> MetricResult:
        """Calculate compliance score against requirements"""
        
        issues = []
        recommendations = []
        compliance_checks = {}
        
        # Check FAR compliance
        far_score = self._check_far_compliance(content)
        compliance_checks['far_compliance'] = far_score.score
        issues.extend(far_score.issues)
        recommendations.extend(far_score.recommendations)
        
        # Check requirement coverage
        req_score = self._check_requirement_coverage(content, requirements)
        compliance_checks['requirement_coverage'] = req_score.score
        issues.extend(req_score.issues)
        recommendations.extend(req_score.recommendations)
        
        # Check format compliance
        format_score = self._check_format_compliance(content, volume_number)
        compliance_checks['format_compliance'] = format_score.score
        issues.extend(format_score.issues)
        recommendations.extend(format_score.recommendations)
        
        # Calculate overall compliance score
        overall_score = statistics.mean(compliance_checks.values())
        
        return MetricResult(
            metric_type=MetricType.COMPLIANCE,
            score=overall_score,
            details=compliance_checks,
            issues=issues,
            recommendations=recommendations
        )
    
    def _check_far_compliance(self, content: str) -> MetricResult:
        """Check Federal Acquisition Regulation compliance"""
        issues = []
        recommendations = []
        score = 1.0
        
        # Check for prohibited terms
        prohibited = self.compliance_rules["far_compliance"]["prohibited_terms"]
        found_prohibited = [term for term in prohibited if term.lower() in content.lower()]
        
        if found_prohibited:
            score -= 0.3
            issues.append(f"Found prohibited terms: {', '.join(found_prohibited)}")
            recommendations.append("Remove or replace prohibited terms with compliant language")
        
        return MetricResult(
            metric_type=MetricType.COMPLIANCE,
            score=score,
            issues=issues,
            recommendations=recommendations
        )
    
    def _check_requirement_coverage(self, content: str, requirements: List[str]) -> MetricResult:
        """Check coverage of specific requirements"""
        issues = []
        recommendations = []
        
        if not requirements:
            return MetricResult(metric_type=MetricType.COMPLIANCE, score=1.0)
        
        covered_requirements = []
        uncovered_requirements = []
        
        for req in requirements:
            # Simple keyword matching - would be more sophisticated in practice
            if any(keyword.lower() in content.lower() for keyword in req.split()):
                covered_requirements.append(req)
            else:
                uncovered_requirements.append(req)
        
        coverage_ratio = len(covered_requirements) / len(requirements)
        
        if uncovered_requirements:
            issues.append(f"Uncovered requirements: {', '.join(uncovered_requirements[:3])}")
            recommendations.append("Address all specified requirements")
        
        return MetricResult(
            metric_type=MetricType.COMPLIANCE,
            score=coverage_ratio,
            details={
                "total_requirements": len(requirements),
                "covered_requirements": len(covered_requirements),
                "coverage_ratio": coverage_ratio
            },
            issues=issues,
            recommendations=recommendations
        )
    
    def _check_format_compliance(self, content: str, volume_number: int) -> MetricResult:
        """Check format compliance requirements"""
        issues = []
        recommendations = []
        score = 1.0
        
        # Check word count as proxy for page count
        words = content.split()
        estimated_pages = len(words) / 250  # Rough estimate
        
        max_pages = self.compliance_rules["format_requirements"]["max_pages"].get(
            f"volume_{volume_number}", 50
        )
        
        if estimated_pages > max_pages:
            score -= 0.2
            issues.append(f"Content may exceed page limit ({estimated_pages:.1f} estimated pages, max {max_pages})")
            recommendations.append("Reduce content to meet page limits")
        
        return MetricResult(
            metric_type=MetricType.COMPLIANCE,
            score=score,
            details={"estimated_pages": estimated_pages, "max_pages": max_pages},
            issues=issues,
            recommendations=recommendations
        )
