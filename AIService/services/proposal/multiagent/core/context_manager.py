"""
Context Manager for Multi-Agent Proposal Generation.
Manages hierarchical context retrieval and caching for improved performance.
"""

import asyncio
import hashlib
import json
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum

from loguru import logger
from services.chroma.chroma_service import ChromaService
from database import get_kontratar_db, get_customer_db


class ContextType(str, Enum):
    OPPORTUNITY = "opportunity"
    REQUIREMENTS = "requirements"
    COMPLIANCE = "compliance"
    TECHNICAL = "technical"
    COMPANY = "company"
    REGULATORY = "regulatory"
    HISTORICAL = "historical"


@dataclass
class ContextItem:
    content: str
    source: str
    relevance_score: float
    context_type: ContextType
    metadata: Dict[str, Any]
    retrieved_at: datetime
    chunk_index: Optional[int] = None


@dataclass
class CachedContext:
    context_items: List[ContextItem]
    query_hash: str
    cached_at: datetime
    expires_at: datetime
    hit_count: int = 0


class ContextManager:
    """
    Advanced context management system that provides hierarchical,
    cached, and intelligent context retrieval for agents.
    """
    
    def __init__(
        self,
        chroma_service: ChromaService,
        cache_ttl_hours: int = 2,
        max_cache_size: int = 1000
    ):
        self.chroma_service = chroma_service
        self.cache_ttl_hours = cache_ttl_hours
        self.max_cache_size = max_cache_size
        
        # Context cache
        self.context_cache: Dict[str, CachedContext] = {}
        
        # Collection mappings for different context types
        self.collection_mappings = {
            ContextType.OPPORTUNITY: self._get_opportunity_collection,
            ContextType.COMPANY: self._get_company_collection,
            ContextType.REGULATORY: self._get_regulatory_collection,
            ContextType.HISTORICAL: self._get_historical_collection
        }
        
        logger.info("Context manager initialized")
    
    def _generate_query_hash(self, query: str, context_type: ContextType, **kwargs) -> str:
        """Generate a hash for caching purposes"""
        cache_key = f"{query}:{context_type}:{json.dumps(kwargs, sort_keys=True)}"
        return hashlib.md5(cache_key.encode()).hexdigest()
    
    def _get_opportunity_collection(self, opportunity_id: str, tenant_id: str, source: str) -> str:
        """Get the ChromaDB collection name for opportunity documents"""
        if source.lower() == "custom":
            return f"{tenant_id}_{opportunity_id}"
        return opportunity_id
    
    def _get_company_collection(self, tenant_id: str, client_short_name: str) -> str:
        """Get the ChromaDB collection name for company documents"""
        return f"{tenant_id}_{client_short_name}"
    
    def _get_regulatory_collection(self) -> str:
        """Get the ChromaDB collection name for regulatory documents"""
        return "regulatory_compliance"
    
    def _get_historical_collection(self, tenant_id: str) -> str:
        """Get the ChromaDB collection name for historical proposals"""
        return f"{tenant_id}_historical_proposals"
    
    async def get_context(
        self,
        query: str,
        context_types: List[ContextType],
        opportunity_id: str,
        tenant_id: str,
        source: str = "custom",
        client_short_name: Optional[str] = None,
        max_chunks_per_type: int = 5,
        use_cache: bool = True
    ) -> List[ContextItem]:
        """
        Retrieve context from multiple sources with intelligent caching.
        
        Args:
            query: The search query
            context_types: List of context types to search
            opportunity_id: Opportunity identifier
            tenant_id: Tenant identifier
            source: Source type (sam, ebuy, custom)
            client_short_name: Client identifier for company context
            max_chunks_per_type: Maximum chunks to retrieve per context type
            use_cache: Whether to use cached results
            
        Returns:
            List of context items sorted by relevance
        """
        
        # Generate cache key
        cache_key = self._generate_query_hash(
            query, 
            str(context_types),
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            client_short_name=client_short_name,
            max_chunks=max_chunks_per_type
        )
        
        # Check cache first
        if use_cache and cache_key in self.context_cache:
            cached = self.context_cache[cache_key]
            if datetime.utcnow() < cached.expires_at:
                cached.hit_count += 1
                logger.debug(f"Cache hit for query: {query[:50]}...")
                return cached.context_items
            else:
                # Remove expired cache entry
                del self.context_cache[cache_key]
        
        # Retrieve context from all requested types
        all_context_items = []
        
        for context_type in context_types:
            try:
                items = await self._retrieve_context_by_type(
                    query=query,
                    context_type=context_type,
                    opportunity_id=opportunity_id,
                    tenant_id=tenant_id,
                    source=source,
                    client_short_name=client_short_name,
                    max_chunks=max_chunks_per_type
                )
                all_context_items.extend(items)
                
            except Exception as e:
                logger.error(f"Error retrieving {context_type} context: {e}")
                continue
        
        # Sort by relevance score
        all_context_items.sort(key=lambda x: x.relevance_score, reverse=True)
        
        # Cache the results
        if use_cache:
            await self._cache_context(cache_key, all_context_items)
        
        logger.info(f"Retrieved {len(all_context_items)} context items for query: {query[:50]}...")
        return all_context_items
    
    async def _retrieve_context_by_type(
        self,
        query: str,
        context_type: ContextType,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: Optional[str],
        max_chunks: int
    ) -> List[ContextItem]:
        """Retrieve context for a specific context type"""
        
        context_items = []
        
        try:
            # Get the appropriate collection name
            if context_type == ContextType.OPPORTUNITY:
                collection_name = self._get_opportunity_collection(opportunity_id, tenant_id, source)
            elif context_type == ContextType.COMPANY and client_short_name:
                collection_name = self._get_company_collection(tenant_id, client_short_name)
            elif context_type == ContextType.REGULATORY:
                collection_name = self._get_regulatory_collection()
            elif context_type == ContextType.HISTORICAL:
                collection_name = self._get_historical_collection(tenant_id)
            elif context_type == ContextType.TECHNICAL:
                collection_name = self._get_technical_collection(tenant_id, opportunity_id)
            elif context_type == ContextType.COMPLIANCE:
                collection_name = self._get_compliance_collection(tenant_id, opportunity_id)
            else:
                logger.warning(f"Unsupported context type: {context_type}")
                return []
            
            # Check if collection exists before querying
            if not await self._collection_exists(collection_name):
                logger.debug(f"Collection {collection_name} does not exist for context type {context_type}")
                return []

            # Retrieve chunks from ChromaDB
            async for db in get_kontratar_db():
                chunks = await self.chroma_service.get_relevant_chunks(
                    db, collection_name, query, n_results=max_chunks
                )
                break
            
            # Convert chunks to ContextItems
            for i, chunk in enumerate(chunks):
                context_item = ContextItem(
                    content=chunk,
                    source=collection_name,
                    relevance_score=1.0 - (i * 0.1),  # Simple relevance scoring
                    context_type=context_type,
                    metadata={
                        "collection": collection_name,
                        "chunk_index": i,
                        "query": query
                    },
                    retrieved_at=datetime.utcnow(),
                    chunk_index=i
                )
                context_items.append(context_item)
            
            logger.debug(f"Retrieved {len(context_items)} items for {context_type}")
            
        except Exception as e:
            logger.warning(f"Could not retrieve context for type {context_type}: {e}")
            # This is not a critical error - continue with empty context for this type

        return context_items
    
    async def get_specialized_context(
        self,
        section_title: str,
        section_description: str,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: Optional[str] = None,
        max_chunks: int = 10
    ) -> Dict[str, List[ContextItem]]:
        """
        Get specialized context for a specific proposal section.
        Returns context organized by type for better agent utilization.
        """
        
        # Generate specialized queries for different context types
        queries = {
            ContextType.OPPORTUNITY: f"Requirements and specifications for {section_title}: {section_description}",
            ContextType.TECHNICAL: f"Technical requirements and constraints for {section_title}",
            ContextType.COMPLIANCE: f"Compliance requirements and regulations for {section_title}",
        }
        
        if client_short_name:
            queries[ContextType.COMPANY] = f"Company capabilities and experience relevant to {section_title}"
        
        # Retrieve context for each type
        specialized_context = {}
        
        for context_type, query in queries.items():
            context_items = await self.get_context(
                query=query,
                context_types=[context_type],
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                source=source,
                client_short_name=client_short_name,
                max_chunks_per_type=max_chunks // len(queries)
            )
            specialized_context[context_type.value] = context_items
        
        return specialized_context
    
    async def _cache_context(self, cache_key: str, context_items: List[ContextItem]):
        """Cache context items with TTL"""
        
        # Clean up cache if it's getting too large
        if len(self.context_cache) >= self.max_cache_size:
            await self._cleanup_cache()
        
        expires_at = datetime.utcnow() + timedelta(hours=self.cache_ttl_hours)
        
        cached_context = CachedContext(
            context_items=context_items,
            query_hash=cache_key,
            cached_at=datetime.utcnow(),
            expires_at=expires_at
        )
        
        self.context_cache[cache_key] = cached_context
        logger.debug(f"Cached context with key: {cache_key}")
    
    async def _cleanup_cache(self):
        """Remove expired and least-used cache entries"""
        now = datetime.utcnow()
        
        # Remove expired entries
        expired_keys = [
            key for key, cached in self.context_cache.items()
            if now >= cached.expires_at
        ]
        
        for key in expired_keys:
            del self.context_cache[key]
        
        # If still too large, remove least-used entries
        if len(self.context_cache) >= self.max_cache_size:
            sorted_cache = sorted(
                self.context_cache.items(),
                key=lambda x: (x[1].hit_count, x[1].cached_at)
            )
            
            # Remove bottom 25%
            remove_count = len(sorted_cache) // 4
            for key, _ in sorted_cache[:remove_count]:
                del self.context_cache[key]
        
        logger.debug(f"Cache cleanup completed. Current size: {len(self.context_cache)}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        total_hits = sum(cached.hit_count for cached in self.context_cache.values())
        
        return {
            "cache_size": len(self.context_cache),
            "max_cache_size": self.max_cache_size,
            "total_hits": total_hits,
            "cache_utilization": len(self.context_cache) / self.max_cache_size,
            "ttl_hours": self.cache_ttl_hours
        }
    
    async def clear_cache(self):
        """Clear all cached context"""
        self.context_cache.clear()
        logger.info("Context cache cleared")
    
    def format_context_for_llm(self, context_items: List[ContextItem], max_length: int = 8000) -> str:
        """
        Format context items for LLM consumption with length limits.
        """
        formatted_sections = []
        current_length = 0
        
        for item in context_items:
            section = f"## {item.context_type.value.title()} Context\n"
            section += f"Source: {item.source}\n"
            section += f"Relevance: {item.relevance_score:.2f}\n\n"
            section += f"{item.content}\n\n"
            
            if current_length + len(section) > max_length:
                break
            
            formatted_sections.append(section)
            current_length += len(section)
        
        return "".join(formatted_sections)

    def _get_technical_collection(self, tenant_id: str, opportunity_id: str) -> str:
        """Get technical context collection name"""
        # For technical context, we primarily use the opportunity collection
        # which contains the RFP technical requirements and specifications
        opportunity_collection = f"{tenant_id}_{opportunity_id}"

        # Could also check for dedicated technical collections like:
        # - f"{tenant_id}_technical_standards"
        # - "technical_specifications"
        # But for now, use the opportunity collection which has the RFP technical content

        return opportunity_collection

    def _get_compliance_collection(self, tenant_id: str, opportunity_id: str) -> str:
        """Get compliance context collection name"""
        # For compliance context, use the opportunity collection which contains
        # RFP compliance requirements and evaluation criteria
        opportunity_collection = f"{tenant_id}_{opportunity_id}"

        # Note: We could also check for dedicated compliance collections like:
        # - "regulatory_compliance" (but this collection may not exist)
        # - f"{tenant_id}_compliance" (tenant-specific compliance standards)
        # But for now, the opportunity collection has the most relevant compliance data

        return opportunity_collection

    async def _collection_exists(self, collection_name: str) -> bool:
        """Check if a ChromaDB collection exists"""
        try:
            async for db in get_kontratar_db():
                # Try to get collection info - this will fail if collection doesn't exist
                await self.chroma_service.get_relevant_chunks(db, collection_name, "test", n_results=1)
                return True
        except Exception as e:
            # Collection doesn't exist or other error
            logger.debug(f"Collection {collection_name} check failed: {e}")
            return False
