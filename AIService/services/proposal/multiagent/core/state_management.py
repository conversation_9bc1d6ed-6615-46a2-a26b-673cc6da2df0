"""
State Management for Multi-Agent Proposal Generation.
Manages the shared state across all agents during proposal generation.
"""

import asyncio
import json
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Set
from dataclasses import dataclass, field

from loguru import logger
from pydantic import BaseModel, Field


class ProposalPhase(str, Enum):
    INITIALIZATION = "initialization"
    REQUIREMENTS_ANALYSIS = "requirements_analysis"
    CONTENT_COMPLIANCE = "content_compliance"
    OUTLINE_GENERATION = "outline_generation"
    CONTENT_GENERATION = "content_generation"
    QUALITY_ASSURANCE = "quality_assurance"
    INTEGRATION = "integration"
    FINALIZATION = "finalization"
    COMPLETED = "completed"
    FAILED = "failed"


class VolumeStatus(str, Enum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    UNDER_REVIEW = "under_review"


@dataclass
class RequirementItem:
    requirement_id: str
    description: str
    priority: str  # HIGH, MEDIUM, LOW
    compliance_status: str  # PENDING, ADDRESSED, FAILED
    source_section: Optional[str] = None
    assigned_volume: Optional[int] = None
    validation_notes: Optional[str] = None


@dataclass
class VolumeState:
    volume_number: int
    status: VolumeStatus = VolumeStatus.NOT_STARTED
    table_of_contents: Optional[List[Dict[str, Any]]] = None
    outline: Optional[List[Dict[str, Any]]] = None
    sections: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    quality_score: float = 0.0
    compliance_score: float = 0.0
    word_count: int = 0
    page_count: int = 0
    last_updated: datetime = field(default_factory=datetime.utcnow)
    assigned_agents: Set[str] = field(default_factory=set)
    validation_errors: List[str] = field(default_factory=list)


class ProposalState(BaseModel):
    """
    Central state object that tracks the entire proposal generation process.
    Shared across all agents and updated throughout the workflow.
    """
    
    # Basic proposal information
    proposal_id: str
    opportunity_id: str
    tenant_id: str
    client_short_name: str
    opportunity_type: str  # sam, ebuy, custom
    is_rfp: bool = True
    
    # Current state
    current_phase: ProposalPhase = ProposalPhase.INITIALIZATION
    started_at: datetime = Field(default_factory=datetime.utcnow)
    last_updated: datetime = Field(default_factory=datetime.utcnow)
    
    # Requirements and compliance
    requirements: List[RequirementItem] = Field(default_factory=list)
    content_compliance: Optional[str] = None
    technical_requirements: Optional[str] = None
    
    # Volume management
    requested_volumes: List[int] = Field(default_factory=lambda: [1, 2, 3, 4, 5])
    volumes: Dict[int, VolumeState] = Field(default_factory=dict)
    
    # Agent tracking
    active_agents: Set[str] = Field(default_factory=set)
    completed_agents: Set[str] = Field(default_factory=set)
    failed_agents: Set[str] = Field(default_factory=set)
    
    # Quality metrics
    overall_quality_score: float = 0.0
    overall_compliance_score: float = 0.0
    
    # Metadata
    metadata: Dict[str, Any] = Field(default_factory=dict)
    errors: List[str] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)
    
    # Configuration
    personality_id: Optional[int] = None
    profile_id: Optional[str] = None
    source_documents: List[str] = Field(default_factory=list)
    
    class Config:
        arbitrary_types_allowed = True
    
    def initialize_volumes(self):
        """Initialize volume states for requested volumes"""
        for volume_num in self.requested_volumes:
            if volume_num not in self.volumes:
                self.volumes[volume_num] = VolumeState(volume_number=volume_num)
        
        logger.info(f"Initialized {len(self.requested_volumes)} volumes for proposal {self.proposal_id}")
    
    def update_phase(self, new_phase: ProposalPhase):
        """Update the current phase of proposal generation"""
        old_phase = self.current_phase
        self.current_phase = new_phase
        self.last_updated = datetime.utcnow()
        
        logger.info(f"Proposal {self.proposal_id} phase changed from {old_phase} to {new_phase}")
    
    def add_requirement(self, requirement: RequirementItem):
        """Add a new requirement to track"""
        self.requirements.append(requirement)
        logger.debug(f"Added requirement {requirement.requirement_id} to proposal {self.proposal_id}")
    
    def update_requirement_status(self, requirement_id: str, status: str, notes: Optional[str] = None):
        """Update the compliance status of a requirement"""
        for req in self.requirements:
            if req.requirement_id == requirement_id:
                req.compliance_status = status
                if notes:
                    req.validation_notes = notes
                logger.debug(f"Updated requirement {requirement_id} status to {status}")
                return
        
        logger.warning(f"Requirement {requirement_id} not found for status update")
    
    def get_volume_state(self, volume_number: int) -> Optional[VolumeState]:
        """Get the state of a specific volume"""
        return self.volumes.get(volume_number)
    
    def update_volume_status(self, volume_number: int, status: VolumeStatus):
        """Update the status of a specific volume"""
        if volume_number in self.volumes:
            self.volumes[volume_number].status = status
            self.volumes[volume_number].last_updated = datetime.utcnow()
            logger.debug(f"Volume {volume_number} status updated to {status}")
    
    def assign_agent_to_volume(self, volume_number: int, agent_id: str):
        """Assign an agent to work on a specific volume"""
        if volume_number in self.volumes:
            self.volumes[volume_number].assigned_agents.add(agent_id)
            logger.debug(f"Agent {agent_id} assigned to volume {volume_number}")
    
    def add_agent_to_active(self, agent_id: str):
        """Mark an agent as active"""
        self.active_agents.add(agent_id)
        self.failed_agents.discard(agent_id)
        self.completed_agents.discard(agent_id)
    
    def mark_agent_completed(self, agent_id: str):
        """Mark an agent as completed"""
        self.active_agents.discard(agent_id)
        self.completed_agents.add(agent_id)
        self.failed_agents.discard(agent_id)
    
    def mark_agent_failed(self, agent_id: str, error_message: str):
        """Mark an agent as failed"""
        self.active_agents.discard(agent_id)
        self.failed_agents.add(agent_id)
        self.completed_agents.discard(agent_id)
        self.errors.append(f"Agent {agent_id}: {error_message}")
    
    def add_error(self, error_message: str):
        """Add an error to the proposal state"""
        self.errors.append(error_message)
        logger.error(f"Proposal {self.proposal_id} error: {error_message}")
    
    def add_warning(self, warning_message: str):
        """Add a warning to the proposal state"""
        self.warnings.append(warning_message)
        logger.warning(f"Proposal {self.proposal_id} warning: {warning_message}")
    
    def calculate_overall_progress(self) -> float:
        """Calculate overall progress as a percentage"""
        if not self.volumes:
            return 0.0
        
        total_volumes = len(self.requested_volumes)
        completed_volumes = sum(
            1 for vol_num in self.requested_volumes 
            if vol_num in self.volumes and self.volumes[vol_num].status == VolumeStatus.COMPLETED
        )
        
        return (completed_volumes / total_volumes) * 100.0
    
    def get_compliance_summary(self) -> Dict[str, Any]:
        """Get a summary of requirement compliance"""
        total_requirements = len(self.requirements)
        if total_requirements == 0:
            return {"total": 0, "addressed": 0, "pending": 0, "failed": 0, "compliance_rate": 0.0}
        
        addressed = sum(1 for req in self.requirements if req.compliance_status == "ADDRESSED")
        pending = sum(1 for req in self.requirements if req.compliance_status == "PENDING")
        failed = sum(1 for req in self.requirements if req.compliance_status == "FAILED")
        
        return {
            "total": total_requirements,
            "addressed": addressed,
            "pending": pending,
            "failed": failed,
            "compliance_rate": (addressed / total_requirements) * 100.0
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert state to dictionary for serialization"""
        return {
            "proposal_id": self.proposal_id,
            "opportunity_id": self.opportunity_id,
            "tenant_id": self.tenant_id,
            "current_phase": self.current_phase,
            "progress": self.calculate_overall_progress(),
            "compliance_summary": self.get_compliance_summary(),
            "volumes": {
                vol_num: {
                    "status": vol_state.status,
                    "quality_score": vol_state.quality_score,
                    "compliance_score": vol_state.compliance_score,
                    "word_count": vol_state.word_count,
                    "validation_errors": vol_state.validation_errors
                }
                for vol_num, vol_state in self.volumes.items()
            },
            "active_agents": list(self.active_agents),
            "completed_agents": list(self.completed_agents),
            "failed_agents": list(self.failed_agents),
            "errors": self.errors,
            "warnings": self.warnings,
            "last_updated": self.last_updated.isoformat()
        }


class StateManager:
    """
    Manages proposal states and provides thread-safe access to shared state.
    """
    
    def __init__(self):
        self.states: Dict[str, ProposalState] = {}
        self.locks: Dict[str, asyncio.Lock] = {}
        
        logger.info("State manager initialized")
    
    async def create_proposal_state(
        self,
        proposal_id: str,
        opportunity_id: str,
        tenant_id: str,
        client_short_name: str,
        opportunity_type: str,
        **kwargs
    ) -> ProposalState:
        """Create a new proposal state"""
        
        state = ProposalState(
            proposal_id=proposal_id,
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            client_short_name=client_short_name,
            opportunity_type=opportunity_type,
            **kwargs
        )
        
        state.initialize_volumes()
        
        self.states[proposal_id] = state
        self.locks[proposal_id] = asyncio.Lock()
        
        logger.info(f"Created proposal state for {proposal_id}")
        return state
    
    async def get_proposal_state(self, proposal_id: str) -> Optional[ProposalState]:
        """Get a proposal state by ID"""
        return self.states.get(proposal_id)
    
    async def update_proposal_state(self, proposal_id: str, state: ProposalState):
        """Update a proposal state"""
        async with self.locks.get(proposal_id, asyncio.Lock()):
            self.states[proposal_id] = state
            state.last_updated = datetime.utcnow()
    
    async def delete_proposal_state(self, proposal_id: str):
        """Delete a proposal state"""
        if proposal_id in self.states:
            del self.states[proposal_id]
        if proposal_id in self.locks:
            del self.locks[proposal_id]
        
        logger.info(f"Deleted proposal state for {proposal_id}")
    
    def get_all_proposal_ids(self) -> List[str]:
        """Get all active proposal IDs"""
        return list(self.states.keys())
    
    def get_states_summary(self) -> Dict[str, Any]:
        """Get a summary of all proposal states"""
        return {
            proposal_id: state.to_dict()
            for proposal_id, state in self.states.items()
        }
