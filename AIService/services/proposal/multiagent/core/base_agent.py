"""
Base Agent class for the multi-agent proposal generation system.
All specialized agents inherit from this base class.
"""

import asyncio
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from typing import Any, Dict, List, Optional, Set
from enum import Enum

from loguru import logger
from pydantic import BaseModel, Field

from .agent_communication import AgentCommunication, Message, MessageType
from .state_management import ProposalState


class AgentStatus(str, Enum):
    IDLE = "idle"
    WORKING = "working"
    WAITING = "waiting"
    COMPLETED = "completed"
    FAILED = "failed"


class AgentCapability(str, Enum):
    REQUIREMENTS_ANALYSIS = "requirements_analysis"
    CONTENT_COMPLIANCE = "content_compliance"
    TECHNICAL_WRITING = "technical_writing"
    QUALITY_ASSURANCE = "quality_assurance"
    INTEGRATION = "integration"
    RESEARCH = "research"
    VALIDATION = "validation"


class AgentMetadata(BaseModel):
    agent_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    name: str
    capabilities: List[AgentCapability]
    status: AgentStatus = AgentStatus.IDLE
    created_at: datetime = Field(default_factory=datetime.utcnow)
    last_activity: datetime = Field(default_factory=datetime.utcnow)
    performance_score: float = 1.0
    error_count: int = 0


class BaseAgent(ABC):
    """
    Base class for all agents in the multi-agent system.
    Provides common functionality for communication, state management, and execution.
    """
    
    def __init__(
        self,
        name: str,
        capabilities: List[AgentCapability],
        communication: AgentCommunication,
        max_retries: int = 3
    ):
        self.metadata = AgentMetadata(name=name, capabilities=capabilities)
        self.communication = communication
        self.max_retries = max_retries
        self.dependencies: Set[str] = set()
        self.dependents: Set[str] = set()
        
        # Register this agent with the communication system
        self.communication.register_agent(self)
        
        logger.info(f"Initialized agent {self.metadata.name} with ID {self.metadata.agent_id}")
    
    @property
    def agent_id(self) -> str:
        return self.metadata.agent_id
    
    @property
    def name(self) -> str:
        return self.metadata.name
    
    @property
    def status(self) -> AgentStatus:
        return self.metadata.status
    
    def update_status(self, status: AgentStatus):
        """Update agent status and last activity timestamp"""
        self.metadata.status = status
        self.metadata.last_activity = datetime.utcnow()
        logger.debug(f"Agent {self.name} status updated to {status}")
    
    def add_dependency(self, agent_id: str):
        """Add a dependency on another agent"""
        self.dependencies.add(agent_id)
        logger.debug(f"Agent {self.name} now depends on agent {agent_id}")
    
    def add_dependent(self, agent_id: str):
        """Add an agent that depends on this one"""
        self.dependents.add(agent_id)
        logger.debug(f"Agent {agent_id} now depends on agent {self.name}")
    
    async def send_message(self, recipient_id: str, message_type: MessageType, content: Dict[str, Any]):
        """Send a message to another agent"""
        message = Message(
            sender_id=self.agent_id,
            recipient_id=recipient_id,
            message_type=message_type,
            content=content
        )
        await self.communication.send_message(message)
    
    async def broadcast_message(self, message_type: MessageType, content: Dict[str, Any]):
        """Broadcast a message to all agents"""
        await self.communication.broadcast_message(self.agent_id, message_type, content)
    
    async def receive_message(self, message: Message) -> Optional[Dict[str, Any]]:
        """
        Handle incoming messages. Override in subclasses for custom message handling.
        """
        logger.debug(f"Agent {self.name} received message of type {message.message_type}")
        return None
    
    @abstractmethod
    async def execute(self, proposal_state: ProposalState, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main execution method for the agent. Must be implemented by subclasses.
        
        Args:
            proposal_state: Current state of the proposal generation
            context: Additional context and data needed for execution
            
        Returns:
            Dict containing the agent's output and any updates to the proposal state
        """
        pass
    
    async def execute_with_retry(self, proposal_state: ProposalState, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the agent with retry logic and error handling.
        """
        self.update_status(AgentStatus.WORKING)
        
        for attempt in range(self.max_retries):
            try:
                logger.info(f"Agent {self.name} starting execution (attempt {attempt + 1}/{self.max_retries})")
                
                result = await self.execute(proposal_state, context)
                
                self.update_status(AgentStatus.COMPLETED)
                self.metadata.performance_score = min(1.0, self.metadata.performance_score + 0.1)
                
                logger.info(f"Agent {self.name} completed successfully")
                return result
                
            except Exception as e:
                self.metadata.error_count += 1
                self.metadata.performance_score = max(0.0, self.metadata.performance_score - 0.2)
                
                logger.error(f"Agent {self.name} failed on attempt {attempt + 1}: {e}")
                
                if attempt == self.max_retries - 1:
                    self.update_status(AgentStatus.FAILED)
                    raise
                
                # Wait before retry
                await asyncio.sleep(2 ** attempt)
        
        self.update_status(AgentStatus.FAILED)
        raise RuntimeError(f"Agent {self.name} failed after {self.max_retries} attempts")
    
    @abstractmethod
    async def validate_input(self, proposal_state: ProposalState, context: Dict[str, Any]) -> bool:
        """
        Validate that the agent has all required inputs to execute.
        
        Args:
            proposal_state: Current proposal state
            context: Execution context
            
        Returns:
            True if inputs are valid, False otherwise
        """
        pass
    
    @abstractmethod
    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """
        Validate the agent's output before returning it.
        
        Args:
            output: The agent's execution output
            
        Returns:
            True if output is valid, False otherwise
        """
        pass
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for this agent"""
        return {
            "agent_id": self.agent_id,
            "name": self.name,
            "status": self.status,
            "performance_score": self.metadata.performance_score,
            "error_count": self.metadata.error_count,
            "last_activity": self.metadata.last_activity,
            "uptime_hours": (datetime.utcnow() - self.metadata.created_at).total_seconds() / 3600
        }
    
    def __repr__(self) -> str:
        return f"<{self.__class__.__name__}(name='{self.name}', status='{self.status}')>"
