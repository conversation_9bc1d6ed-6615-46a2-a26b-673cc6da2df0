"""
Agent Communication System for multi-agent coordination.
Handles message passing, event broadcasting, and agent coordination.
"""

import asyncio
import uuid
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Set
from collections import defaultdict, deque

from loguru import logger
from pydantic import BaseModel, Field


class MessageType(str, Enum):
    TASK_REQUEST = "task_request"
    TASK_RESPONSE = "task_response"
    DATA_SHARE = "data_share"
    STATUS_UPDATE = "status_update"
    ERROR_REPORT = "error_report"
    VALIDATION_REQUEST = "validation_request"
    VALIDATION_RESPONSE = "validation_response"
    COORDINATION = "coordination"
    BROADCAST = "broadcast"


class MessagePriority(str, Enum):
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"


class Message(BaseModel):
    message_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    sender_id: str
    recipient_id: str
    message_type: MessageType
    priority: MessagePriority = MessagePriority.NORMAL
    content: Dict[str, Any]
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    correlation_id: Optional[str] = None  # For tracking related messages
    requires_response: bool = False
    response_timeout: Optional[int] = None  # seconds


class AgentCommunication:
    """
    Central communication hub for agent coordination.
    Manages message routing, queuing, and delivery between agents.
    """
    
    def __init__(self, max_queue_size: int = 1000):
        self.agents: Dict[str, Any] = {}  # agent_id -> agent instance
        self.message_queues: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_queue_size))
        self.message_history: List[Message] = []
        self.pending_responses: Dict[str, asyncio.Future] = {}
        self.subscribers: Dict[MessageType, Set[str]] = defaultdict(set)
        self.running = False
        self.message_processor_task: Optional[asyncio.Task] = None
        
        logger.info("Agent communication system initialized")
    
    def register_agent(self, agent):
        """Register an agent with the communication system"""
        self.agents[agent.agent_id] = agent
        logger.info(f"Registered agent {agent.name} with ID {agent.agent_id}")
    
    def unregister_agent(self, agent_id: str):
        """Unregister an agent from the communication system"""
        if agent_id in self.agents:
            agent_name = self.agents[agent_id].name
            del self.agents[agent_id]
            # Clear message queue
            if agent_id in self.message_queues:
                del self.message_queues[agent_id]
            logger.info(f"Unregistered agent {agent_name} with ID {agent_id}")
    
    def subscribe_to_message_type(self, agent_id: str, message_type: MessageType):
        """Subscribe an agent to receive messages of a specific type"""
        self.subscribers[message_type].add(agent_id)
        logger.debug(f"Agent {agent_id} subscribed to {message_type} messages")
    
    def unsubscribe_from_message_type(self, agent_id: str, message_type: MessageType):
        """Unsubscribe an agent from a message type"""
        self.subscribers[message_type].discard(agent_id)
        logger.debug(f"Agent {agent_id} unsubscribed from {message_type} messages")
    
    async def send_message(self, message: Message) -> Optional[Any]:
        """
        Send a message to a specific agent.
        If requires_response is True, wait for and return the response.
        """
        if message.recipient_id not in self.agents:
            logger.error(f"Cannot send message: recipient {message.recipient_id} not found")
            return None
        
        # Add to message history
        self.message_history.append(message)
        
        # Add to recipient's queue
        self.message_queues[message.recipient_id].append(message)
        
        logger.debug(f"Message {message.message_id} queued for agent {message.recipient_id}")
        
        # If response is required, wait for it
        if message.requires_response:
            future = asyncio.Future()
            self.pending_responses[message.message_id] = future
            
            try:
                if message.response_timeout:
                    response = await asyncio.wait_for(future, timeout=message.response_timeout)
                else:
                    response = await future
                return response
            except asyncio.TimeoutError:
                logger.warning(f"Message {message.message_id} timed out waiting for response")
                del self.pending_responses[message.message_id]
                return None
        
        return None
    
    async def send_response(self, original_message_id: str, response_content: Dict[str, Any]):
        """Send a response to a message that required one"""
        if original_message_id in self.pending_responses:
            future = self.pending_responses[original_message_id]
            if not future.done():
                future.set_result(response_content)
            del self.pending_responses[original_message_id]
            logger.debug(f"Response sent for message {original_message_id}")
    
    async def broadcast_message(self, sender_id: str, message_type: MessageType, content: Dict[str, Any]):
        """Broadcast a message to all subscribed agents"""
        subscribers = self.subscribers.get(message_type, set())
        
        for recipient_id in subscribers:
            if recipient_id != sender_id:  # Don't send to self
                message = Message(
                    sender_id=sender_id,
                    recipient_id=recipient_id,
                    message_type=message_type,
                    content=content
                )
                await self.send_message(message)
        
        logger.debug(f"Broadcast message of type {message_type} sent to {len(subscribers)} agents")
    
    async def get_messages(self, agent_id: str, limit: Optional[int] = None) -> List[Message]:
        """Get pending messages for an agent"""
        if agent_id not in self.message_queues:
            return []
        
        messages = []
        queue = self.message_queues[agent_id]
        
        count = 0
        while queue and (limit is None or count < limit):
            messages.append(queue.popleft())
            count += 1
        
        return messages
    
    async def process_agent_messages(self, agent_id: str):
        """Process all pending messages for a specific agent"""
        if agent_id not in self.agents:
            return
        
        agent = self.agents[agent_id]
        messages = await self.get_messages(agent_id)
        
        for message in messages:
            try:
                response = await agent.receive_message(message)
                
                # If the original message required a response, send it
                if message.requires_response and response is not None:
                    await self.send_response(message.message_id, response)
                    
            except Exception as e:
                logger.error(f"Error processing message {message.message_id} for agent {agent_id}: {e}")
    
    async def start_message_processor(self):
        """Start the background message processor"""
        if self.running:
            return
        
        self.running = True
        self.message_processor_task = asyncio.create_task(self._message_processor_loop())
        logger.info("Message processor started")
    
    async def stop_message_processor(self):
        """Stop the background message processor"""
        if not self.running:
            return
        
        self.running = False
        if self.message_processor_task:
            self.message_processor_task.cancel()
            try:
                await self.message_processor_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Message processor stopped")
    
    async def _message_processor_loop(self):
        """Background loop to process messages for all agents"""
        while self.running:
            try:
                # Process messages for all agents
                for agent_id in list(self.agents.keys()):
                    await self.process_agent_messages(agent_id)
                
                # Small delay to prevent busy waiting
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error in message processor loop: {e}")
                await asyncio.sleep(1)
    
    def get_communication_stats(self) -> Dict[str, Any]:
        """Get communication system statistics"""
        total_queued = sum(len(queue) for queue in self.message_queues.values())
        
        return {
            "registered_agents": len(self.agents),
            "total_messages_sent": len(self.message_history),
            "messages_queued": total_queued,
            "pending_responses": len(self.pending_responses),
            "running": self.running
        }
    
    def clear_message_history(self, older_than_hours: int = 24):
        """Clear old messages from history"""
        cutoff_time = datetime.utcnow().timestamp() - (older_than_hours * 3600)
        
        original_count = len(self.message_history)
        self.message_history = [
            msg for msg in self.message_history 
            if msg.timestamp.timestamp() > cutoff_time
        ]
        
        cleared_count = original_count - len(self.message_history)
        if cleared_count > 0:
            logger.info(f"Cleared {cleared_count} old messages from history")
