"""
Multi-Agent Orchestrator for Proposal Generation.
Coordinates the execution of specialized agents in the correct sequence.
"""

import asyncio
import json
from datetime import datetime
from typing import Any, Dict, List, Optional, Set
from dataclasses import dataclass

from loguru import logger

from .core import (
    BaseAgent, AgentCommunication, Message, MessageType,
    ProposalState, StateManager, ContextManager,
    QualityMetrics, ComplianceMetrics, ProposalPhase, VolumeStatus
)
from .agents import (
    RequirementsAnalysisAgent,
    ContentComplianceAgent,
    TechnicalWritingAgent,
    QualityAssuranceAgent,
    IntegrationAgent
)
from services.chroma.chroma_service import ChromaService


@dataclass
class OrchestrationConfig:
    max_parallel_agents: int = 3
    quality_threshold: float = 0.8
    compliance_threshold: float = 0.9
    max_iterations: int = 3
    timeout_minutes: int = 60


class MultiAgentOrchestrator:
    """
    Central orchestrator that manages the multi-agent proposal generation workflow.
    Coordinates agent execution, manages dependencies, and ensures quality standards.
    """
    
    def __init__(
        self,
        chroma_service: ChromaService,
        config: Optional[OrchestrationConfig] = None
    ):
        self.config = config or OrchestrationConfig()
        
        # Core components
        self.communication = AgentCommunication()
        self.state_manager = StateManager()
        self.context_manager = ContextManager(chroma_service)
        self.quality_metrics = QualityMetrics()
        self.compliance_metrics = ComplianceMetrics()
        
        # Agent registry
        self.agents: Dict[str, BaseAgent] = {}
        self.agent_dependencies: Dict[str, Set[str]] = {}
        
        # Execution tracking
        self.active_proposals: Set[str] = set()
        self.execution_history: List[Dict[str, Any]] = []
        
        # Initialize agents
        self._initialize_agents()
        
        logger.info("Multi-agent orchestrator initialized")
    
    def _initialize_agents(self):
        """Initialize all specialized agents"""
        
        # Requirements Analysis Agent
        requirements_agent = RequirementsAnalysisAgent(
            communication=self.communication,
            context_manager=self.context_manager
        )
        self.agents[requirements_agent.agent_id] = requirements_agent
        
        # Content Compliance Agent
        compliance_agent = ContentComplianceAgent(
            communication=self.communication,
            context_manager=self.context_manager
        )
        self.agents[compliance_agent.agent_id] = compliance_agent
        
        # Technical Writing Agents (one per volume type)
        for volume_num in range(1, 6):
            writing_agent = TechnicalWritingAgent(
                communication=self.communication,
                context_manager=self.context_manager,
                volume_specialization=volume_num
            )
            self.agents[writing_agent.agent_id] = writing_agent
        
        # Quality Assurance Agent
        qa_agent = QualityAssuranceAgent(
            communication=self.communication,
            quality_metrics=self.quality_metrics,
            compliance_metrics=self.compliance_metrics
        )
        self.agents[qa_agent.agent_id] = qa_agent
        
        # Integration Agent
        integration_agent = IntegrationAgent(
            communication=self.communication,
            state_manager=self.state_manager
        )
        self.agents[integration_agent.agent_id] = integration_agent
        
        # Set up dependencies
        self._setup_agent_dependencies()
        
        logger.info(f"Initialized {len(self.agents)} agents")
    
    def _setup_agent_dependencies(self):
        """Set up agent execution dependencies"""
        
        # Find agents by type
        requirements_agents = [a for a in self.agents.values() if "Requirements" in a.name]
        compliance_agents = [a for a in self.agents.values() if "Compliance" in a.name]
        writing_agents = [a for a in self.agents.values() if "Writing" in a.name]
        qa_agents = [a for a in self.agents.values() if "QA" in a.name or "Quality" in a.name]
        integration_agents = [a for a in self.agents.values() if "Integration" in a.name]
        
        # Set up dependency chain:
        # Requirements -> Compliance -> Writing -> QA -> Integration
        
        for compliance_agent in compliance_agents:
            for req_agent in requirements_agents:
                compliance_agent.add_dependency(req_agent.agent_id)
        
        for writing_agent in writing_agents:
            for compliance_agent in compliance_agents:
                writing_agent.add_dependency(compliance_agent.agent_id)
        
        for qa_agent in qa_agents:
            for writing_agent in writing_agents:
                qa_agent.add_dependency(writing_agent.agent_id)
        
        for integration_agent in integration_agents:
            for qa_agent in qa_agents:
                integration_agent.add_dependency(qa_agent.agent_id)
    
    async def generate_proposal(
        self,
        job_instruction: str,
        job_submitted_by: str
    ) -> Dict[str, Any]:
        """
        Main entry point for proposal generation.
        Orchestrates the entire multi-agent workflow.
        """
        
        try:
            # Parse job instruction
            job = json.loads(job_instruction)
            
            # Create proposal state
            proposal_state = await self._create_proposal_state(job, job_submitted_by)
            
            # Start communication system
            await self.communication.start_message_processor()
            
            # Execute the multi-agent workflow
            result = await self._execute_workflow(proposal_state)
            
            # Stop communication system
            await self.communication.stop_message_processor()
            
            return result
            
        except Exception as e:
            logger.error(f"Error in proposal generation: {e}")
            raise
    
    async def _create_proposal_state(self, job: Dict[str, Any], job_submitted_by: str) -> ProposalState:
        """Create and initialize proposal state from job instruction"""
        
        # Extract required fields
        opportunity_id = job.get("opportunityId")
        tenant_id = job.get("tenantId")
        client_short_name = job.get("clientShortName")
        opportunity_type = job.get("opportunityType")
        
        if not all([opportunity_id, tenant_id, client_short_name, opportunity_type]):
            raise ValueError("Missing required fields in job instruction")
        
        # Generate proposal ID
        proposal_id = f"{tenant_id}_{opportunity_id}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
        
        # Create proposal state
        proposal_state = await self.state_manager.create_proposal_state(
            proposal_id=proposal_id,
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            client_short_name=client_short_name,
            opportunity_type=opportunity_type,
            is_rfp=job.get("is_rfp", True),
            requested_volumes=job.get("generatedVolumes", [1, 2, 3, 4, 5]),
            personality_id=job.get("aiPersonalityId"),
            profile_id=job.get("profileId"),
            source_documents=job.get("sourceDocuments", [])
        )
        
        self.active_proposals.add(proposal_id)
        
        logger.info(f"Created proposal state for {proposal_id}")
        return proposal_state
    
    async def _execute_workflow(self, proposal_state: ProposalState) -> Dict[str, Any]:
        """Execute the complete multi-agent workflow"""
        
        workflow_phases = [
            (ProposalPhase.REQUIREMENTS_ANALYSIS, self._execute_requirements_phase),
            (ProposalPhase.CONTENT_COMPLIANCE, self._execute_compliance_phase),
            (ProposalPhase.CONTENT_GENERATION, self._execute_writing_phase),
            (ProposalPhase.QUALITY_ASSURANCE, self._execute_qa_phase),
            (ProposalPhase.INTEGRATION, self._execute_integration_phase)
        ]
        
        try:
            for phase, phase_executor in workflow_phases:
                logger.info(f"Starting phase: {phase}")
                proposal_state.update_phase(phase)
                
                await phase_executor(proposal_state)
                
                # Check if we should continue
                if proposal_state.current_phase == ProposalPhase.FAILED:
                    break
            
            # Mark as completed if we made it through all phases
            if proposal_state.current_phase != ProposalPhase.FAILED:
                proposal_state.update_phase(ProposalPhase.COMPLETED)
            
            # Generate final result
            result = await self._generate_final_result(proposal_state)
            
            return result
            
        except Exception as e:
            proposal_state.update_phase(ProposalPhase.FAILED)
            proposal_state.add_error(f"Workflow execution failed: {str(e)}")
            raise
        
        finally:
            self.active_proposals.discard(proposal_state.proposal_id)
    
    async def _execute_requirements_phase(self, proposal_state: ProposalState):
        """Execute requirements analysis phase"""
        
        # Find requirements analysis agents
        req_agents = [a for a in self.agents.values() if "Requirements" in a.name]
        
        if not req_agents:
            raise RuntimeError("No requirements analysis agents available")
        
        # Execute requirements analysis
        for agent in req_agents:
            try:
                proposal_state.add_agent_to_active(agent.agent_id)
                
                context = {
                    "opportunity_id": proposal_state.opportunity_id,
                    "tenant_id": proposal_state.tenant_id,
                    "source": proposal_state.opportunity_type
                }
                
                result = await agent.execute_with_retry(proposal_state, context)
                
                # Update proposal state with requirements
                if "requirements" in result:
                    for req_data in result["requirements"]:
                        from .core.state_management import RequirementItem
                        requirement = RequirementItem(
                            requirement_id=req_data.get("id", f"req_{len(proposal_state.requirements)}"),
                            description=req_data.get("description", ""),
                            priority=req_data.get("priority", "MEDIUM"),
                            compliance_status="PENDING"
                        )
                        proposal_state.add_requirement(requirement)
                
                proposal_state.mark_agent_completed(agent.agent_id)
                
            except Exception as e:
                proposal_state.mark_agent_failed(agent.agent_id, str(e))
                logger.error(f"Requirements agent {agent.name} failed: {e}")
    
    async def _execute_compliance_phase(self, proposal_state: ProposalState):
        """Execute content compliance phase"""
        
        compliance_agents = [a for a in self.agents.values() if "Compliance" in a.name]
        
        for agent in compliance_agents:
            try:
                proposal_state.add_agent_to_active(agent.agent_id)
                
                context = {
                    "opportunity_id": proposal_state.opportunity_id,
                    "tenant_id": proposal_state.tenant_id,
                    "source": proposal_state.opportunity_type,
                    "requirements": proposal_state.requirements
                }
                
                result = await agent.execute_with_retry(proposal_state, context)
                
                # Update proposal state with compliance information
                if "content_compliance" in result:
                    proposal_state.content_compliance = result["content_compliance"]
                
                proposal_state.mark_agent_completed(agent.agent_id)
                
            except Exception as e:
                proposal_state.mark_agent_failed(agent.agent_id, str(e))
                logger.error(f"Compliance agent {agent.name} failed: {e}")
    
    async def _execute_writing_phase(self, proposal_state: ProposalState):
        """Execute content writing phase"""
        
        writing_agents = [a for a in self.agents.values() if "Writing" in a.name]
        
        # Execute writing agents in parallel for different volumes
        tasks = []
        
        for volume_num in proposal_state.requested_volumes:
            # Find agent specialized for this volume
            volume_agent = next(
                (a for a in writing_agents if hasattr(a, 'volume_specialization') and a.volume_specialization == volume_num),
                writing_agents[0] if writing_agents else None
            )
            
            if volume_agent:
                proposal_state.update_volume_status(volume_num, VolumeStatus.IN_PROGRESS)
                proposal_state.assign_agent_to_volume(volume_num, volume_agent.agent_id)
                
                context = {
                    "volume_number": volume_num,
                    "opportunity_id": proposal_state.opportunity_id,
                    "tenant_id": proposal_state.tenant_id,
                    "source": proposal_state.opportunity_type,
                    "client_short_name": proposal_state.client_short_name,
                    "requirements": proposal_state.requirements,
                    "content_compliance": proposal_state.content_compliance
                }
                
                task = self._execute_writing_agent(volume_agent, proposal_state, context, volume_num)
                tasks.append(task)
        
        # Wait for all writing tasks to complete
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _execute_writing_agent(
        self,
        agent: BaseAgent,
        proposal_state: ProposalState,
        context: Dict[str, Any],
        volume_num: int
    ):
        """Execute a single writing agent"""
        try:
            proposal_state.add_agent_to_active(agent.agent_id)
            
            result = await agent.execute_with_retry(proposal_state, context)
            
            # Update volume state with generated content
            volume_state = proposal_state.get_volume_state(volume_num)
            if volume_state and "sections" in result:
                volume_state.sections = result["sections"]
                volume_state.status = VolumeStatus.COMPLETED
            
            proposal_state.mark_agent_completed(agent.agent_id)
            
        except Exception as e:
            proposal_state.mark_agent_failed(agent.agent_id, str(e))
            proposal_state.update_volume_status(volume_num, VolumeStatus.FAILED)
            logger.error(f"Writing agent {agent.name} failed for volume {volume_num}: {e}")
    
    async def _execute_qa_phase(self, proposal_state: ProposalState):
        """Execute quality assurance phase"""
        
        qa_agents = [a for a in self.agents.values() if "QA" in a.name or "Quality" in a.name]
        
        for agent in qa_agents:
            try:
                proposal_state.add_agent_to_active(agent.agent_id)
                
                context = {
                    "proposal_state": proposal_state,
                    "quality_threshold": self.config.quality_threshold,
                    "compliance_threshold": self.config.compliance_threshold
                }
                
                result = await agent.execute_with_retry(proposal_state, context)
                
                # Update quality scores
                if "quality_scores" in result:
                    for volume_num, score in result["quality_scores"].items():
                        volume_state = proposal_state.get_volume_state(int(volume_num))
                        if volume_state:
                            volume_state.quality_score = score
                
                proposal_state.mark_agent_completed(agent.agent_id)
                
            except Exception as e:
                proposal_state.mark_agent_failed(agent.agent_id, str(e))
                logger.error(f"QA agent {agent.name} failed: {e}")
    
    async def _execute_integration_phase(self, proposal_state: ProposalState):
        """Execute integration phase"""
        
        integration_agents = [a for a in self.agents.values() if "Integration" in a.name]
        
        for agent in integration_agents:
            try:
                proposal_state.add_agent_to_active(agent.agent_id)
                
                context = {
                    "proposal_state": proposal_state,
                    "output_format": "encrypted_json"
                }
                
                result = await agent.execute_with_retry(proposal_state, context)
                
                proposal_state.mark_agent_completed(agent.agent_id)
                
            except Exception as e:
                proposal_state.mark_agent_failed(agent.agent_id, str(e))
                logger.error(f"Integration agent {agent.name} failed: {e}")
    
    async def _generate_final_result(self, proposal_state: ProposalState) -> Dict[str, Any]:
        """Generate the final result summary"""
        
        return {
            "proposal_id": proposal_state.proposal_id,
            "status": proposal_state.current_phase,
            "progress": proposal_state.calculate_overall_progress(),
            "compliance_summary": proposal_state.get_compliance_summary(),
            "volumes_completed": [
                vol_num for vol_num, vol_state in proposal_state.volumes.items()
                if vol_state.status == VolumeStatus.COMPLETED
            ],
            "quality_scores": {
                vol_num: vol_state.quality_score
                for vol_num, vol_state in proposal_state.volumes.items()
            },
            "errors": proposal_state.errors,
            "warnings": proposal_state.warnings,
            "execution_time": (datetime.utcnow() - proposal_state.started_at).total_seconds(),
            "agents_used": len(proposal_state.completed_agents),
            "agents_failed": len(proposal_state.failed_agents)
        }
    
    def get_orchestrator_status(self) -> Dict[str, Any]:
        """Get current orchestrator status"""
        return {
            "active_proposals": len(self.active_proposals),
            "total_agents": len(self.agents),
            "communication_stats": self.communication.get_communication_stats(),
            "cache_stats": self.context_manager.get_cache_stats(),
            "config": {
                "max_parallel_agents": self.config.max_parallel_agents,
                "quality_threshold": self.config.quality_threshold,
                "compliance_threshold": self.config.compliance_threshold
            }
        }
