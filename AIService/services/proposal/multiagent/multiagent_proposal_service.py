"""
Multi-Agent Proposal Generation Service.
Main service that replaces the monolithic RFP generation approach.
"""

import json
import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger

from .orchestrator import MultiAgentOrchestrator, OrchestrationConfig
from .core import ProposalPhase
from services.chroma.chroma_service import ChromaService
from services.proposal.proposal_decoding_service import ProposalDecodingService
from database import get_customer_db, get_kontratar_db


class MultiAgentProposalService:
    """
    Main service for multi-agent proposal generation.
    Provides a clean interface that replaces the existing RFP generation service.
    """
    
    def __init__(self):
        self.chroma_service = ChromaService("http://ai.kontratar.com:5000", None)
        self.proposal_decoding_service = ProposalDecodingService()
        
        # Initialize orchestrator with default configuration
        self.orchestrator = MultiAgentOrchestrator(
            chroma_service=self.chroma_service,
            config=OrchestrationConfig(
                max_parallel_agents=3,
                quality_threshold=0.8,
                compliance_threshold=0.9,
                max_iterations=3,
                timeout_minutes=60
            )
        )
        
        logger.info("Multi-Agent Proposal Service initialized")
    
    async def generate_rfp(self, job_instruction: str, job_submitted_by: str) -> Dict[str, Any]:
        """
        Main entry point for RFP generation using multi-agent system.
        
        Args:
            job_instruction: JSON string containing job parameters
            job_submitted_by: User who submitted the job
            
        Returns:
            Dict containing generation results and status
        """
        
        start_time = datetime.now()
        
        try:
            logger.info(f"Starting multi-agent RFP generation for job submitted by {job_submitted_by}")
            
            # Parse and validate job instruction
            job_data = await self._parse_and_validate_job_instruction(job_instruction)
            
            # Execute multi-agent workflow
            result = await self.orchestrator.generate_proposal(job_instruction, job_submitted_by)
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # Prepare final response
            response = {
                "status": "SUCCESS" if result.get("status") != "FAILED" else "FAILED",
                "proposal_id": result.get("proposal_id"),
                "execution_time_seconds": execution_time,
                "volumes_generated": result.get("volumes_completed", []),
                "quality_summary": result.get("quality_scores", {}),
                "compliance_summary": result.get("compliance_summary", {}),
                "agent_performance": {
                    "agents_used": result.get("agents_used", 0),
                    "agents_failed": result.get("agents_failed", 0)
                },
                "errors": result.get("errors", []),
                "warnings": result.get("warnings", []),
                "job_instruction": job_instruction,
                "job_submitted_by": job_submitted_by,
                "generation_timestamp": datetime.now().isoformat()
            }
            
            logger.info(f"Multi-agent RFP generation completed in {execution_time:.2f} seconds")
            return response
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"Multi-agent RFP generation failed after {execution_time:.2f} seconds: {e}")
            
            return {
                "status": "FAILED",
                "error": str(e),
                "execution_time_seconds": execution_time,
                "job_instruction": job_instruction,
                "job_submitted_by": job_submitted_by,
                "generation_timestamp": datetime.now().isoformat()
            }
    
    async def generate_rfi(self, job_instruction: str, job_submitted_by: str) -> Dict[str, Any]:
        """
        Generate RFI using multi-agent system.
        
        Args:
            job_instruction: JSON string containing job parameters
            job_submitted_by: User who submitted the job
            
        Returns:
            Dict containing generation results and status
        """
        
        # For now, RFI generation uses the same multi-agent approach as RFP
        # In the future, this could be specialized for RFI-specific requirements
        
        logger.info(f"Starting multi-agent RFI generation for job submitted by {job_submitted_by}")
        
        # Modify job instruction to indicate RFI
        try:
            job_data = json.loads(job_instruction)
            job_data["is_rfp"] = False
            modified_job_instruction = json.dumps(job_data)
            
            return await self.generate_rfp(modified_job_instruction, job_submitted_by)
            
        except Exception as e:
            logger.error(f"Error in RFI generation: {e}")
            return {
                "status": "FAILED",
                "error": f"RFI generation failed: {str(e)}",
                "job_instruction": job_instruction,
                "job_submitted_by": job_submitted_by,
                "generation_timestamp": datetime.now().isoformat()
            }
    
    async def _parse_and_validate_job_instruction(self, job_instruction: str) -> Dict[str, Any]:
        """Parse and validate job instruction"""
        
        try:
            job_data = json.loads(job_instruction)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in job instruction: {e}")
        
        # Validate required fields
        required_fields = [
            "opportunityId",
            "tenantId", 
            "clientShortName",
            "opportunityType"
        ]
        
        missing_fields = [field for field in required_fields if field not in job_data]
        if missing_fields:
            raise ValueError(f"Missing required fields in job instruction: {missing_fields}")
        
        # Validate opportunity type
        valid_types = ["sam", "ebuy", "custom"]
        if job_data["opportunityType"].lower() not in valid_types:
            raise ValueError(f"Invalid opportunity type: {job_data['opportunityType']}. Must be one of {valid_types}")
        
        # Set defaults for optional fields
        job_data.setdefault("is_rfp", True)
        job_data.setdefault("generatedVolumes", [1, 2, 3, 4, 5])
        job_data.setdefault("sourceDocuments", [])
        job_data.setdefault("setForReview", True)
        
        return job_data
    
    async def get_proposal_status(self, proposal_id: str) -> Dict[str, Any]:
        """
        Get the current status of a proposal generation.
        
        Args:
            proposal_id: The proposal ID to check
            
        Returns:
            Dict containing current status and progress
        """
        
        try:
            proposal_state = await self.orchestrator.state_manager.get_proposal_state(proposal_id)
            
            if not proposal_state:
                return {
                    "status": "NOT_FOUND",
                    "error": f"Proposal {proposal_id} not found"
                }
            
            return {
                "status": "SUCCESS",
                "proposal_id": proposal_id,
                "current_phase": proposal_state.current_phase,
                "progress_percentage": proposal_state.calculate_overall_progress(),
                "volumes_status": {
                    vol_num: {
                        "status": vol_state.status,
                        "quality_score": vol_state.quality_score,
                        "compliance_score": vol_state.compliance_score,
                        "sections_count": len(vol_state.sections)
                    }
                    for vol_num, vol_state in proposal_state.volumes.items()
                },
                "active_agents": list(proposal_state.active_agents),
                "completed_agents": list(proposal_state.completed_agents),
                "failed_agents": list(proposal_state.failed_agents),
                "errors": proposal_state.errors,
                "warnings": proposal_state.warnings,
                "last_updated": proposal_state.last_updated.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting proposal status for {proposal_id}: {e}")
            return {
                "status": "ERROR",
                "error": str(e)
            }
    
    async def cancel_proposal_generation(self, proposal_id: str) -> Dict[str, Any]:
        """
        Cancel an ongoing proposal generation.
        
        Args:
            proposal_id: The proposal ID to cancel
            
        Returns:
            Dict containing cancellation status
        """
        
        try:
            proposal_state = await self.orchestrator.state_manager.get_proposal_state(proposal_id)
            
            if not proposal_state:
                return {
                    "status": "NOT_FOUND",
                    "error": f"Proposal {proposal_id} not found"
                }
            
            if proposal_state.current_phase in [ProposalPhase.COMPLETED, ProposalPhase.FAILED]:
                return {
                    "status": "ALREADY_FINISHED",
                    "message": f"Proposal {proposal_id} is already {proposal_state.current_phase}"
                }
            
            # Update proposal state to failed
            proposal_state.update_phase(ProposalPhase.FAILED)
            proposal_state.add_error("Proposal generation cancelled by user")
            
            # Update state in manager
            await self.orchestrator.state_manager.update_proposal_state(proposal_id, proposal_state)
            
            return {
                "status": "CANCELLED",
                "proposal_id": proposal_id,
                "message": "Proposal generation cancelled successfully"
            }
            
        except Exception as e:
            logger.error(f"Error cancelling proposal {proposal_id}: {e}")
            return {
                "status": "ERROR",
                "error": str(e)
            }
    
    async def get_system_status(self) -> Dict[str, Any]:
        """
        Get the current status of the multi-agent system.
        
        Returns:
            Dict containing system status and performance metrics
        """
        
        try:
            orchestrator_status = self.orchestrator.get_orchestrator_status()
            
            return {
                "status": "OPERATIONAL",
                "system_info": {
                    "service_name": "Multi-Agent Proposal Service",
                    "version": "1.0.0",
                    "startup_time": datetime.now().isoformat()
                },
                "orchestrator_status": orchestrator_status,
                "agent_count": orchestrator_status.get("total_agents", 0),
                "active_proposals": orchestrator_status.get("active_proposals", 0),
                "communication_stats": orchestrator_status.get("communication_stats", {}),
                "cache_performance": orchestrator_status.get("cache_stats", {}),
                "configuration": orchestrator_status.get("config", {})
            }
            
        except Exception as e:
            logger.error(f"Error getting system status: {e}")
            return {
                "status": "ERROR",
                "error": str(e)
            }
    
    async def update_configuration(self, config_updates: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update system configuration.
        
        Args:
            config_updates: Dictionary of configuration updates
            
        Returns:
            Dict containing update status
        """
        
        try:
            current_config = self.orchestrator.config
            
            # Update allowed configuration parameters
            allowed_updates = [
                "max_parallel_agents",
                "quality_threshold", 
                "compliance_threshold",
                "max_iterations",
                "timeout_minutes"
            ]
            
            updated_fields = []
            for field, value in config_updates.items():
                if field in allowed_updates:
                    setattr(current_config, field, value)
                    updated_fields.append(field)
            
            return {
                "status": "SUCCESS",
                "updated_fields": updated_fields,
                "current_config": {
                    "max_parallel_agents": current_config.max_parallel_agents,
                    "quality_threshold": current_config.quality_threshold,
                    "compliance_threshold": current_config.compliance_threshold,
                    "max_iterations": current_config.max_iterations,
                    "timeout_minutes": current_config.timeout_minutes
                }
            }
            
        except Exception as e:
            logger.error(f"Error updating configuration: {e}")
            return {
                "status": "ERROR",
                "error": str(e)
            }
    
    async def cleanup_old_proposals(self, older_than_hours: int = 24) -> Dict[str, Any]:
        """
        Clean up old proposal states from memory.
        
        Args:
            older_than_hours: Remove proposals older than this many hours
            
        Returns:
            Dict containing cleanup results
        """
        
        try:
            all_proposal_ids = self.orchestrator.state_manager.get_all_proposal_ids()
            cleaned_count = 0
            
            cutoff_time = datetime.now().timestamp() - (older_than_hours * 3600)
            
            for proposal_id in all_proposal_ids:
                proposal_state = await self.orchestrator.state_manager.get_proposal_state(proposal_id)
                
                if proposal_state and proposal_state.started_at.timestamp() < cutoff_time:
                    if proposal_state.current_phase in [ProposalPhase.COMPLETED, ProposalPhase.FAILED]:
                        await self.orchestrator.state_manager.delete_proposal_state(proposal_id)
                        cleaned_count += 1
            
            return {
                "status": "SUCCESS",
                "cleaned_proposals": cleaned_count,
                "remaining_proposals": len(self.orchestrator.state_manager.get_all_proposal_ids())
            }
            
        except Exception as e:
            logger.error(f"Error cleaning up old proposals: {e}")
            return {
                "status": "ERROR",
                "error": str(e)
            }
