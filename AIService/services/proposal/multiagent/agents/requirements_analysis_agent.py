"""
Requirements Analysis Agent for Multi-Agent Proposal Generation.
Specializes in extracting, analyzing, and structuring RFP requirements.
"""

import json
from typing import Any, Dict, List, Optional
from langchain_ollama import ChatOllama

from loguru import logger

from ..core import (
    BaseAgent, AgentCapability, AgentCommunication, 
    ProposalState, ContextManager, ContextType
)
from database import get_kontratar_db


class RequirementsAnalysisAgent(BaseAgent):
    """
    Specialized agent for comprehensive requirements analysis.
    Extracts, categorizes, and prioritizes requirements from RFP documents.
    """
    
    def __init__(
        self,
        communication: AgentCommunication,
        context_manager: ContextManager,
        llm_api_url: str = "http://ai.kontratar.com:11434"
    ):
        super().__init__(
            name="Requirements Analysis Agent",
            capabilities=[
                AgentCapability.REQUIREMENTS_ANALYSIS,
                AgentCapability.RESEARCH,
                AgentCapability.VALIDATION
            ],
            communication=communication
        )
        
        self.context_manager = context_manager
        self.llm = ChatOllama(
            model="gemma3:27b",
            num_ctx=8000,
            temperature=0.1,
            base_url=llm_api_url
        )
        
        logger.info(f"Initialized {self.name}")
    
    async def execute(self, proposal_state: ProposalState, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute requirements analysis for the proposal.
        
        Returns:
            Dict containing extracted requirements, priorities, and analysis
        """
        
        opportunity_id = context["opportunity_id"]
        tenant_id = context["tenant_id"]
        source = context["source"]
        
        logger.info(f"Starting requirements analysis for {opportunity_id}")
        
        # Step 1: Extract raw requirements from documents
        raw_requirements = await self._extract_raw_requirements(
            opportunity_id, tenant_id, source
        )
        
        # Step 2: Categorize and structure requirements
        structured_requirements = await self._structure_requirements(raw_requirements)
        
        # Step 3: Prioritize requirements
        prioritized_requirements = await self._prioritize_requirements(structured_requirements)
        
        # Step 4: Create requirement traceability matrix
        traceability_matrix = await self._create_traceability_matrix(prioritized_requirements)
        
        # Step 5: Identify compliance checkpoints
        compliance_checkpoints = await self._identify_compliance_checkpoints(prioritized_requirements)
        
        result = {
            "requirements": prioritized_requirements,
            "traceability_matrix": traceability_matrix,
            "compliance_checkpoints": compliance_checkpoints,
            "analysis_summary": {
                "total_requirements": len(prioritized_requirements),
                "critical_requirements": len([r for r in prioritized_requirements if r.get("priority") == "CRITICAL"]),
                "high_requirements": len([r for r in prioritized_requirements if r.get("priority") == "HIGH"]),
                "medium_requirements": len([r for r in prioritized_requirements if r.get("priority") == "MEDIUM"]),
                "low_requirements": len([r for r in prioritized_requirements if r.get("priority") == "LOW"])
            }
        }
        
        logger.info(f"Requirements analysis completed: {len(prioritized_requirements)} requirements identified")
        return result
    
    async def _extract_raw_requirements(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str
    ) -> List[str]:
        """Extract raw requirements from RFP documents"""
        
        # Get comprehensive context about requirements
        context_items = await self.context_manager.get_context(
            query="""
            Extract all requirements, specifications, deliverables, constraints, 
            compliance requirements, technical specifications, performance requirements,
            and evaluation criteria. Include SHALL, MUST, WILL, SHOULD statements.
            """,
            context_types=[ContextType.OPPORTUNITY, ContextType.TECHNICAL, ContextType.COMPLIANCE],
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            max_chunks_per_type=15
        )
        
        if not context_items:
            logger.warning("No context items found for requirements extraction")
            return []
        
        # Format context for LLM
        context_text = self.context_manager.format_context_for_llm(context_items, max_length=12000)
        
        system_prompt = """
        **Role:** Expert Government Contracting Requirements Analyst
        
        **Task:** Extract ALL requirements from the provided RFP/RFI documents.
        
        **Instructions:**
        1. Identify every requirement, specification, deliverable, and constraint
        2. Look for SHALL, MUST, WILL, SHOULD, MAY statements
        3. Extract technical specifications and performance requirements
        4. Identify compliance and regulatory requirements
        5. Find evaluation criteria and scoring factors
        6. Include formatting and submission requirements
        7. Extract timeline and milestone requirements
        
        **Output Format:**
        Return a JSON array where each requirement is a separate string.
        Each requirement should be complete and self-contained.
        
        Example:
        [
            "The contractor SHALL provide 24/7 technical support",
            "All deliverables MUST be submitted in PDF format",
            "The system WILL support 1000 concurrent users"
        ]
        """
        
        user_prompt = f"""
        Extract all requirements from the following RFP/RFI documents:
        
        <documents>
        {context_text}
        </documents>
        
        Return ONLY the JSON array of requirements. Do not include any other text.
        """
        
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # Parse JSON response
            requirements_json = response.content.strip()
            if requirements_json.startswith("```json"):
                requirements_json = requirements_json.split("```json")[1].split("```")[0]
            elif requirements_json.startswith("```"):
                requirements_json = requirements_json.split("```")[1].split("```")[0]
            
            requirements = json.loads(requirements_json)
            
            if not isinstance(requirements, list):
                logger.error("LLM did not return a list of requirements")
                return []
            
            logger.info(f"Extracted {len(requirements)} raw requirements")
            return requirements
            
        except Exception as e:
            logger.error(f"Error extracting requirements: {e}")
            return []
    
    async def _structure_requirements(self, raw_requirements: List[str]) -> List[Dict[str, Any]]:
        """Structure and categorize requirements"""
        
        if not raw_requirements:
            return []
        
        system_prompt = """
        **Role:** Requirements Structuring Expert
        
        **Task:** Structure and categorize requirements into a standardized format.
        
        **Categories:**
        - TECHNICAL: Technical specifications, system requirements, performance
        - FUNCTIONAL: Functional requirements, features, capabilities
        - COMPLIANCE: Regulatory, legal, certification requirements
        - OPERATIONAL: Support, maintenance, training requirements
        - DELIVERY: Timeline, deliverables, submission requirements
        - EVALUATION: Scoring criteria, evaluation factors
        - CONTRACTUAL: Terms, conditions, business requirements
        
        **Priority Levels:**
        - CRITICAL: Mandatory requirements (SHALL, MUST)
        - HIGH: Important requirements (WILL, SHOULD)
        - MEDIUM: Preferred requirements (SHOULD)
        - LOW: Optional requirements (MAY, COULD)
        
        **Output Format:**
        Return a JSON array of requirement objects with this structure:
        {
            "id": "REQ_001",
            "text": "Original requirement text",
            "category": "TECHNICAL",
            "priority": "CRITICAL",
            "type": "SHALL|MUST|WILL|SHOULD|MAY",
            "volume_assignment": 1,
            "verification_method": "inspection|test|analysis|demonstration",
            "keywords": ["keyword1", "keyword2"]
        }
        """
        
        # Process requirements in batches to avoid token limits
        batch_size = 20
        structured_requirements = []
        
        for i in range(0, len(raw_requirements), batch_size):
            batch = raw_requirements[i:i + batch_size]
            
            user_prompt = f"""
            Structure and categorize these requirements:
            
            {json.dumps(batch, indent=2)}
            
            Return ONLY the JSON array of structured requirements.
            Start requirement IDs from REQ_{i+1:03d}.
            """
            
            try:
                messages = [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ]
                
                response = await self.llm.ainvoke(messages)
                
                # Parse JSON response
                batch_json = response.content.strip()
                if batch_json.startswith("```json"):
                    batch_json = batch_json.split("```json")[1].split("```")[0]
                elif batch_json.startswith("```"):
                    batch_json = batch_json.split("```")[1].split("```")[0]
                
                batch_structured = json.loads(batch_json)
                
                if isinstance(batch_structured, list):
                    structured_requirements.extend(batch_structured)
                
            except Exception as e:
                logger.error(f"Error structuring requirements batch {i}: {e}")
                # Fallback: create basic structure
                for j, req in enumerate(batch):
                    structured_requirements.append({
                        "id": f"REQ_{i+j+1:03d}",
                        "text": req,
                        "category": "TECHNICAL",
                        "priority": "HIGH",
                        "type": "SHALL" if "shall" in req.lower() else "SHOULD",
                        "volume_assignment": 1,
                        "verification_method": "inspection",
                        "keywords": []
                    })
        
        logger.info(f"Structured {len(structured_requirements)} requirements")
        return structured_requirements
    
    async def _prioritize_requirements(self, structured_requirements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Prioritize requirements based on criticality and impact"""
        
        # Sort by priority and add priority scores
        priority_scores = {
            "CRITICAL": 4,
            "HIGH": 3,
            "MEDIUM": 2,
            "LOW": 1
        }
        
        for req in structured_requirements:
            req["priority_score"] = priority_scores.get(req.get("priority", "MEDIUM"), 2)
            
            # Boost priority for certain keywords
            critical_keywords = ["security", "compliance", "mandatory", "required", "shall", "must"]
            if any(keyword in req.get("text", "").lower() for keyword in critical_keywords):
                req["priority_score"] = min(4, req["priority_score"] + 1)
        
        # Sort by priority score (highest first)
        structured_requirements.sort(key=lambda x: x.get("priority_score", 0), reverse=True)
        
        return structured_requirements
    
    async def _create_traceability_matrix(self, requirements: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Create requirement traceability matrix"""
        
        matrix = {
            "requirements_by_volume": {},
            "requirements_by_category": {},
            "verification_methods": {},
            "total_requirements": len(requirements)
        }
        
        for req in requirements:
            # Group by volume
            volume = req.get("volume_assignment", 1)
            if volume not in matrix["requirements_by_volume"]:
                matrix["requirements_by_volume"][volume] = []
            matrix["requirements_by_volume"][volume].append(req["id"])
            
            # Group by category
            category = req.get("category", "TECHNICAL")
            if category not in matrix["requirements_by_category"]:
                matrix["requirements_by_category"][category] = []
            matrix["requirements_by_category"][category].append(req["id"])
            
            # Group by verification method
            method = req.get("verification_method", "inspection")
            if method not in matrix["verification_methods"]:
                matrix["verification_methods"][method] = []
            matrix["verification_methods"][method].append(req["id"])
        
        return matrix
    
    async def _identify_compliance_checkpoints(self, requirements: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identify key compliance checkpoints"""
        
        compliance_requirements = [
            req for req in requirements 
            if req.get("category") == "COMPLIANCE" or req.get("priority") == "CRITICAL"
        ]
        
        checkpoints = []
        for req in compliance_requirements:
            checkpoint = {
                "requirement_id": req["id"],
                "checkpoint_name": f"Compliance Check: {req['id']}",
                "description": req["text"],
                "verification_method": req.get("verification_method", "inspection"),
                "responsible_volume": req.get("volume_assignment", 1),
                "mandatory": req.get("priority") in ["CRITICAL", "HIGH"]
            }
            checkpoints.append(checkpoint)
        
        return checkpoints
    
    async def validate_input(self, proposal_state: ProposalState, context: Dict[str, Any]) -> bool:
        """Validate that required inputs are available"""
        required_fields = ["opportunity_id", "tenant_id", "source"]
        return all(field in context for field in required_fields)
    
    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """Validate the agent's output"""
        required_keys = ["requirements", "traceability_matrix", "compliance_checkpoints"]
        return all(key in output for key in required_keys)
