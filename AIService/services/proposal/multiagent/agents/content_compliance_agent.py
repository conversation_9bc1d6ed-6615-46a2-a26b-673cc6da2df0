"""
Content Compliance Agent for Multi-Agent Proposal Generation.
Specializes in analyzing RFP compliance requirements and generating compliance matrices.
"""

import json
from typing import Any, Dict, List, Optional
from langchain_ollama import ChatOllama

from loguru import logger

from ..core import (
    BaseAgent, AgentCapability, AgentCommunication, 
    ProposalState, ContextManager, ContextType
)


class ContentComplianceAgent(BaseAgent):
    """
    Specialized agent for content compliance analysis.
    Analyzes RFP requirements and generates detailed compliance guidance.
    """
    
    def __init__(
        self,
        communication: AgentCommunication,
        context_manager: ContextManager,
        llm_api_url: str = "http://ai.kontratar.com:11434"
    ):
        super().__init__(
            name="Content Compliance Agent",
            capabilities=[
                AgentCapability.CONTENT_COMPLIANCE,
                AgentCapability.VALIDATION,
                AgentCapability.RESEARCH
            ],
            communication=communication
        )
        
        self.context_manager = context_manager
        self.llm = ChatOllama(
            model="gemma3:27b",
            num_ctx=8000,
            temperature=0.0,
            base_url=llm_api_url
        )
        
        logger.info(f"Initialized {self.name}")
    
    async def execute(self, proposal_state: ProposalState, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute content compliance analysis for the proposal.
        
        Returns:
            Dict containing compliance requirements, volume mappings, and guidance
        """
        
        opportunity_id = context["opportunity_id"]
        tenant_id = context["tenant_id"]
        source = context["source"]
        requirements = context.get("requirements", [])
        
        logger.info(f"Starting content compliance analysis for {opportunity_id}")
        
        # Step 1: Analyze volume structure requirements
        volume_structure = await self._analyze_volume_structure(
            opportunity_id, tenant_id, source
        )
        
        # Step 2: Generate content compliance matrix
        compliance_matrix = await self._generate_compliance_matrix(
            opportunity_id, tenant_id, source, requirements
        )
        
        # Step 3: Create volume-specific guidance
        volume_guidance = await self._create_volume_guidance(
            volume_structure, compliance_matrix
        )
        
        # Step 4: Identify mandatory content elements
        mandatory_elements = await self._identify_mandatory_elements(
            opportunity_id, tenant_id, source
        )
        
        # Step 5: Generate evaluation criteria mapping
        evaluation_mapping = await self._map_evaluation_criteria(
            opportunity_id, tenant_id, source, requirements
        )
        
        result = {
            "content_compliance": {
                "volume_structure": volume_structure,
                "compliance_matrix": compliance_matrix,
                "volume_guidance": volume_guidance,
                "mandatory_elements": mandatory_elements,
                "evaluation_mapping": evaluation_mapping
            },
            "compliance_summary": {
                "total_volumes": len(volume_structure),
                "mandatory_sections": len(mandatory_elements),
                "evaluation_factors": len(evaluation_mapping)
            }
        }
        
        logger.info(f"Content compliance analysis completed for {len(volume_structure)} volumes")
        return result
    
    async def _analyze_volume_structure(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str
    ) -> Dict[int, Dict[str, Any]]:
        """Analyze required volume structure from RFP"""
        
        # Get context about volume requirements
        context_items = await self.context_manager.get_context(
            query="""
            Volume structure, proposal organization, required sections for each volume,
            table of contents requirements, volume-specific instructions, page limits,
            formatting requirements for each volume.
            """,
            context_types=[ContextType.OPPORTUNITY, ContextType.COMPLIANCE],
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            max_chunks_per_type=10
        )
        
        context_text = self.context_manager.format_context_for_llm(context_items, max_length=10000)
        
        system_prompt = """
        **Role:** Government Proposal Structure Expert
        
        **Task:** Analyze RFP documents to determine the required volume structure and organization.
        
        **Instructions:**
        1. Identify how many volumes are required
        2. Determine what content goes in each volume
        3. Extract specific section requirements for each volume
        4. Identify page limits and formatting requirements
        5. Note any special instructions for each volume
        
        **Output Format:**
        Return a JSON object where keys are volume numbers (1, 2, 3, etc.) and values contain:
        {
            "1": {
                "title": "Technical Volume",
                "description": "Technical approach and methodology",
                "required_sections": ["Executive Summary", "Technical Approach", "Management Plan"],
                "page_limit": 50,
                "special_instructions": ["Include technical diagrams", "Address all SOW tasks"],
                "evaluation_weight": 60
            }
        }
        """
        
        user_prompt = f"""
        Analyze the volume structure requirements from these RFP documents:
        
        <documents>
        {context_text}
        </documents>
        
        Return ONLY the JSON object with volume structure. If no specific volumes are mentioned,
        use standard government proposal structure (Volume 1: Technical, Volume 2: Cost, Volume 3: Past Performance).
        """
        
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # Parse JSON response
            structure_json = response.content.strip()
            if structure_json.startswith("```json"):
                structure_json = structure_json.split("```json")[1].split("```")[0]
            elif structure_json.startswith("```"):
                structure_json = structure_json.split("```")[1].split("```")[0]
            
            volume_structure = json.loads(structure_json)
            
            # Convert string keys to integers
            volume_structure = {int(k): v for k, v in volume_structure.items()}
            
            logger.info(f"Analyzed structure for {len(volume_structure)} volumes")
            return volume_structure
            
        except Exception as e:
            logger.error(f"Error analyzing volume structure: {e}")
            # Return default structure
            return {
                1: {
                    "title": "Technical Volume",
                    "description": "Technical approach and methodology",
                    "required_sections": ["Executive Summary", "Technical Approach", "Management Plan"],
                    "page_limit": 50,
                    "special_instructions": [],
                    "evaluation_weight": 60
                },
                2: {
                    "title": "Cost Volume", 
                    "description": "Cost proposal and pricing",
                    "required_sections": ["Cost Summary", "Basis of Estimate"],
                    "page_limit": 20,
                    "special_instructions": [],
                    "evaluation_weight": 30
                },
                3: {
                    "title": "Past Performance Volume",
                    "description": "Past performance and experience",
                    "required_sections": ["Past Performance Matrix", "Reference Details"],
                    "page_limit": 30,
                    "special_instructions": [],
                    "evaluation_weight": 10
                }
            }
    
    async def _generate_compliance_matrix(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        requirements: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Generate detailed compliance matrix"""
        
        # Get compliance-specific context
        context_items = await self.context_manager.get_context(
            query="""
            Compliance requirements, evaluation criteria, mandatory clauses,
            certification requirements, regulatory compliance, submission requirements,
            format requirements, deadline requirements.
            """,
            context_types=[ContextType.COMPLIANCE, ContextType.REGULATORY],
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            max_chunks_per_type=8
        )
        
        context_text = self.context_manager.format_context_for_llm(context_items, max_length=8000)
        
        # Create compliance matrix
        compliance_matrix = {
            "regulatory_requirements": [],
            "format_requirements": {},
            "submission_requirements": {},
            "evaluation_criteria": [],
            "mandatory_clauses": [],
            "certification_requirements": []
        }
        
        # Process requirements to build matrix
        for req in requirements:
            # RequirementItem is a dataclass, access attributes directly
            req_id = req.requirement_id
            req_text = req.description
            req_priority = req.priority

            # Determine category from description content (since RequirementItem doesn't have category)
            req_category = "TECHNICAL"  # Default
            if any(word in req_text.lower() for word in ["compliance", "regulation", "standard", "certif"]):
                req_category = "COMPLIANCE"
            elif any(word in req_text.lower() for word in ["evaluat", "criteria", "scoring", "weight"]):
                req_category = "EVALUATION"

            if req_category == "COMPLIANCE":
                if "format" in req_text.lower() or "page" in req_text.lower():
                    compliance_matrix["format_requirements"][req_id] = req_text
                elif "submit" in req_text.lower() or "deadline" in req_text.lower():
                    compliance_matrix["submission_requirements"][req_id] = req_text
                elif "certif" in req_text.lower():
                    compliance_matrix["certification_requirements"].append({
                        "id": req_id,
                        "requirement": req_text,
                        "priority": req_priority
                    })
                else:
                    compliance_matrix["regulatory_requirements"].append({
                        "id": req_id,
                        "requirement": req_text,
                        "priority": req_priority
                    })

            elif req_category == "EVALUATION":
                compliance_matrix["evaluation_criteria"].append({
                    "id": req_id,
                    "criterion": req_text,
                    "weight": "Not specified"  # RequirementItem doesn't have evaluation_weight
                })
        
        return compliance_matrix
    
    async def _create_volume_guidance(
        self,
        volume_structure: Dict[int, Dict[str, Any]],
        compliance_matrix: Dict[str, Any]
    ) -> Dict[int, Dict[str, Any]]:
        """Create detailed guidance for each volume"""
        
        volume_guidance = {}
        
        for volume_num, volume_info in volume_structure.items():
            guidance = {
                "content_requirements": volume_info.get("required_sections", []),
                "page_limit": volume_info.get("page_limit", 50),
                "special_instructions": volume_info.get("special_instructions", []),
                "compliance_items": [],
                "evaluation_focus": [],
                "formatting_requirements": []
            }
            
            # Add relevant compliance items
            for req in compliance_matrix.get("regulatory_requirements", []):
                if volume_num == 1 or "all volumes" in req["requirement"].lower():
                    guidance["compliance_items"].append(req)
            
            # Add evaluation criteria
            for criterion in compliance_matrix.get("evaluation_criteria", []):
                if volume_num == 1:  # Most evaluation criteria apply to technical volume
                    guidance["evaluation_focus"].append(criterion)
            
            # Add format requirements
            for req_id, req_text in compliance_matrix.get("format_requirements", {}).items():
                guidance["formatting_requirements"].append({
                    "id": req_id,
                    "requirement": req_text
                })
            
            volume_guidance[volume_num] = guidance
        
        return volume_guidance
    
    async def _identify_mandatory_elements(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str
    ) -> List[Dict[str, Any]]:
        """Identify mandatory content elements"""
        
        context_items = await self.context_manager.get_context(
            query="""
            Mandatory sections, required content, must include, shall contain,
            mandatory attachments, required forms, essential elements.
            """,
            context_types=[ContextType.OPPORTUNITY, ContextType.COMPLIANCE],
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            max_chunks_per_type=6
        )
        
        context_text = self.context_manager.format_context_for_llm(context_items, max_length=6000)
        
        system_prompt = """
        **Role:** Compliance Requirements Specialist
        
        **Task:** Identify all mandatory content elements that MUST be included in the proposal.
        
        **Instructions:**
        1. Look for SHALL, MUST, REQUIRED language
        2. Identify mandatory sections and subsections
        3. Find required forms and attachments
        4. Note mandatory certifications
        5. Identify required formatting elements
        
        **Output Format:**
        Return a JSON array of mandatory elements:
        [
            {
                "element": "Executive Summary",
                "description": "Must provide overview of approach",
                "location": "Volume 1",
                "type": "section",
                "mandatory_level": "SHALL"
            }
        ]
        """
        
        user_prompt = f"""
        Identify mandatory content elements from these documents:
        
        <documents>
        {context_text}
        </documents>
        
        Return ONLY the JSON array of mandatory elements.
        """
        
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # Parse JSON response
            elements_json = response.content.strip()
            if elements_json.startswith("```json"):
                elements_json = elements_json.split("```json")[1].split("```")[0]
            elif elements_json.startswith("```"):
                elements_json = elements_json.split("```")[1].split("```")[0]
            
            mandatory_elements = json.loads(elements_json)
            
            if not isinstance(mandatory_elements, list):
                return []
            
            return mandatory_elements
            
        except Exception as e:
            logger.error(f"Error identifying mandatory elements: {e}")
            return []
    
    async def _map_evaluation_criteria(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        requirements: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Map evaluation criteria to proposal sections"""
        
        evaluation_requirements = [
            req for req in requirements
            if "evaluat" in req.description.lower() or "criteria" in req.description.lower()
        ]

        evaluation_mapping = []

        for req in evaluation_requirements:
            mapping = {
                "criterion_id": req.requirement_id,
                "criterion": req.description,
                "weight": "Not specified",  # RequirementItem doesn't have evaluation_weight
                "target_volume": req.assigned_volume or 1,
                "target_sections": [],
                "scoring_method": "Not specified"
            }
            
            # Determine target sections based on criterion content
            criterion_text = req["text"].lower()
            if "technical" in criterion_text:
                mapping["target_sections"] = ["Technical Approach", "Technical Solution"]
            elif "management" in criterion_text:
                mapping["target_sections"] = ["Management Plan", "Project Management"]
            elif "experience" in criterion_text or "past performance" in criterion_text:
                mapping["target_sections"] = ["Past Performance", "Experience"]
                mapping["target_volume"] = 3
            elif "cost" in criterion_text or "price" in criterion_text:
                mapping["target_sections"] = ["Cost Proposal", "Pricing"]
                mapping["target_volume"] = 2
            
            evaluation_mapping.append(mapping)
        
        return evaluation_mapping
    
    async def validate_input(self, proposal_state: ProposalState, context: Dict[str, Any]) -> bool:
        """Validate that required inputs are available"""
        required_fields = ["opportunity_id", "tenant_id", "source"]
        return all(field in context for field in required_fields)
    
    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """Validate the agent's output"""
        return "content_compliance" in output and isinstance(output["content_compliance"], dict)
