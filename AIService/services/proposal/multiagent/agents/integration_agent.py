"""
Integration Agent for Multi-Agent Proposal Generation.
Specializes in final integration, formatting, and delivery preparation.
"""

import json
import asyncio
from datetime import datetime
from typing import Any, Dict, List, Optional
from langchain_ollama import ChatOllama

from loguru import logger

from ..core import (
    BaseAgent, AgentCapability, AgentCommunication, 
    ProposalState, StateManager, VolumeStatus
)
from services.proposal.proposal_decoding_service import ProposalDecodingService
from controllers.customer.proposals_in_review_controller import ProposalsInReviewController
from database import get_customer_db


class IntegrationAgent(BaseAgent):
    """
    Specialized agent for final proposal integration and delivery.
    Handles formatting, encryption, storage, and delivery preparation.
    """
    
    def __init__(
        self,
        communication: AgentCommunication,
        state_manager: StateManager,
        llm_api_url: str = "http://ai.kontratar.com:11434"
    ):
        super().__init__(
            name="Integration Agent",
            capabilities=[
                AgentCapability.INTEGRATION,
                AgentCapability.VALIDATION
            ],
            communication=communication
        )
        
        self.state_manager = state_manager
        self.proposal_decoding_service = ProposalDecodingService()
        self.llm = ChatOllama(
            model="gemma3:27b",
            num_ctx=8000,
            temperature=0.0,
            base_url=llm_api_url
        )
        
        logger.info(f"Initialized {self.name}")
    
    async def execute(self, proposal_state: ProposalState, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute final integration and delivery preparation.
        
        Returns:
            Dict containing integration results and delivery status
        """
        
        output_format = context.get("output_format", "encrypted_json")
        
        logger.info(f"Starting integration for proposal {proposal_state.proposal_id}")
        
        # Step 1: Validate all volumes are ready
        validation_result = await self._validate_volumes_ready(proposal_state)
        
        if not validation_result["all_ready"]:
            logger.error(f"Not all volumes ready for integration: {validation_result['issues']}")
            return {
                "status": "FAILED",
                "error": "Not all volumes ready for integration",
                "validation_result": validation_result
            }
        
        # Step 2: Generate final table of contents for each volume
        final_tocs = await self._generate_final_table_of_contents(proposal_state)
        
        # Step 3: Apply final formatting and consistency checks
        formatted_volumes = await self._apply_final_formatting(proposal_state, final_tocs)
        
        # Step 4: Generate executive summaries if missing
        enhanced_volumes = await self._enhance_volumes_with_summaries(formatted_volumes, proposal_state)
        
        # Step 5: Perform final cross-volume integration
        integrated_volumes = await self._perform_cross_volume_integration(enhanced_volumes, proposal_state)
        
        # Step 6: Generate cover pages and front matter
        final_volumes = await self._add_cover_pages_and_front_matter(integrated_volumes, proposal_state)
        
        # Step 7: Encrypt and store volumes
        storage_result = await self._encrypt_and_store_volumes(final_volumes, proposal_state)
        
        # Step 8: Generate delivery package
        delivery_package = await self._generate_delivery_package(final_volumes, proposal_state, storage_result)
        
        result = {
            "status": "SUCCESS",
            "validation_result": validation_result,
            "final_volumes": final_volumes,
            "storage_result": storage_result,
            "delivery_package": delivery_package,
            "integration_summary": {
                "total_volumes": len(final_volumes),
                "total_sections": sum(len(vol.get("sections", {})) for vol in final_volumes.values()),
                "total_word_count": sum(vol.get("metadata", {}).get("word_count", 0) for vol in final_volumes.values()),
                "integration_timestamp": datetime.utcnow().isoformat()
            }
        }
        
        logger.info(f"Integration completed for proposal {proposal_state.proposal_id}")
        return result
    
    async def _validate_volumes_ready(self, proposal_state: ProposalState) -> Dict[str, Any]:
        """Validate that all volumes are ready for integration"""
        
        validation_result = {
            "all_ready": True,
            "volume_status": {},
            "issues": [],
            "warnings": []
        }
        
        for volume_num in proposal_state.requested_volumes:
            volume_state = proposal_state.get_volume_state(volume_num)
            
            if not volume_state:
                validation_result["all_ready"] = False
                validation_result["issues"].append(f"Volume {volume_num} not found")
                validation_result["volume_status"][volume_num] = "MISSING"
                continue
            
            if volume_state.status != VolumeStatus.COMPLETED:
                validation_result["all_ready"] = False
                validation_result["issues"].append(f"Volume {volume_num} status is {volume_state.status}")
                validation_result["volume_status"][volume_num] = volume_state.status
                continue
            
            if not volume_state.sections:
                validation_result["all_ready"] = False
                validation_result["issues"].append(f"Volume {volume_num} has no sections")
                validation_result["volume_status"][volume_num] = "NO_CONTENT"
                continue
            
            # Check quality scores (handle both float and dict types)
            quality_score = volume_state.quality_score
            if isinstance(quality_score, dict):
                # If it's a dict, try to get an average or overall score
                quality_score = quality_score.get("overall", quality_score.get("average", 0.8))

            compliance_score = volume_state.compliance_score
            if isinstance(compliance_score, dict):
                # If it's a dict, try to get an average or overall score
                compliance_score = compliance_score.get("overall", compliance_score.get("average", 0.8))

            if quality_score < 0.7:
                validation_result["warnings"].append(f"Volume {volume_num} quality score is low ({quality_score:.2f})")

            if compliance_score < 0.8:
                validation_result["warnings"].append(f"Volume {volume_num} compliance score is low ({compliance_score:.2f})")
            
            validation_result["volume_status"][volume_num] = "READY"
        
        return validation_result
    
    async def _generate_final_table_of_contents(self, proposal_state: ProposalState) -> Dict[int, List[Dict[str, Any]]]:
        """Generate final table of contents for each volume"""
        
        final_tocs = {}
        
        for volume_num, volume_state in proposal_state.volumes.items():
            if volume_state.status != VolumeStatus.COMPLETED:
                continue
            
            # Generate TOC from actual sections
            toc = []
            section_number = 1
            
            for section_title, section_data in volume_state.sections.items():
                toc_entry = {
                    "section_number": f"{section_number}.0",
                    "title": section_title,
                    "page_number": "TBD",  # Would be calculated during final formatting
                    "word_count": section_data.get("word_count", 0),
                    "subsections": []
                }
                
                # Add subsections if they exist
                subsections = section_data.get("subsections", [])
                for i, subsection in enumerate(subsections):
                    toc_entry["subsections"].append({
                        "section_number": f"{section_number}.{i+1}",
                        "title": subsection.get("title", f"Subsection {i+1}"),
                        "page_number": "TBD"
                    })
                
                toc.append(toc_entry)
                section_number += 1
            
            final_tocs[volume_num] = toc
        
        return final_tocs
    
    async def _apply_final_formatting(
        self,
        proposal_state: ProposalState,
        final_tocs: Dict[int, List[Dict[str, Any]]]
    ) -> Dict[int, Dict[str, Any]]:
        """Apply final formatting and consistency to all volumes"""
        
        formatted_volumes = {}
        
        for volume_num, volume_state in proposal_state.volumes.items():
            if volume_state.status != VolumeStatus.COMPLETED:
                continue
            
            formatted_volume = {
                "volume_number": volume_num,
                "table_of_contents": final_tocs.get(volume_num, []),
                "sections": {},
                "metadata": {
                    "quality_score": volume_state.quality_score,
                    "compliance_score": volume_state.compliance_score,
                    "word_count": 0,
                    "page_count": 0,
                    "last_updated": volume_state.last_updated.isoformat()
                }
            }
            
            total_word_count = 0
            
            # Format each section
            for section_title, section_data in volume_state.sections.items():
                formatted_section = await self._format_section(section_data, volume_num, section_title)
                formatted_volume["sections"][section_title] = formatted_section
                total_word_count += formatted_section.get("word_count", 0)
            
            # Update metadata
            formatted_volume["metadata"]["word_count"] = total_word_count
            formatted_volume["metadata"]["page_count"] = total_word_count / 250  # Rough estimate
            
            formatted_volumes[volume_num] = formatted_volume
        
        return formatted_volumes
    
    async def _format_section(self, section_data: Dict[str, Any], volume_num: int, section_title: str) -> Dict[str, Any]:
        """Apply consistent formatting to a section"""
        
        content = section_data.get("content", "")
        
        # Apply basic formatting improvements
        formatted_content = await self._apply_content_formatting(content, section_title)
        
        formatted_section = {
            "title": section_title,
            "content": formatted_content,
            "word_count": len(formatted_content.split()),
            "requirements_addressed": section_data.get("requirements_addressed", []),
            "win_themes": section_data.get("win_themes", []),
            "quality_score": section_data.get("quality_score", 0.8),
            "formatting_applied": True
        }
        
        return formatted_section
    
    async def _apply_content_formatting(self, content: str, section_title: str) -> str:
        """Apply consistent formatting to content"""
        
        # Basic formatting improvements
        formatted_content = content
        
        # Ensure proper paragraph spacing
        formatted_content = formatted_content.replace("\n\n\n", "\n\n")
        
        # Ensure section title is properly formatted
        if not formatted_content.startswith(f"# {section_title}") and not formatted_content.startswith(f"## {section_title}"):
            formatted_content = f"## {section_title}\n\n{formatted_content}"
        
        # Add proper spacing around headers
        import re
        formatted_content = re.sub(r'\n(#{1,6}[^\n]+)\n', r'\n\n\1\n\n', formatted_content)
        
        # Clean up extra whitespace
        formatted_content = re.sub(r'\n\s*\n\s*\n', '\n\n', formatted_content)
        
        return formatted_content.strip()
    
    async def _enhance_volumes_with_summaries(
        self,
        formatted_volumes: Dict[int, Dict[str, Any]],
        proposal_state: ProposalState
    ) -> Dict[int, Dict[str, Any]]:
        """Add executive summaries and volume overviews where missing"""
        
        enhanced_volumes = formatted_volumes.copy()
        
        for volume_num, volume_data in enhanced_volumes.items():
            sections = volume_data.get("sections", {})
            
            # Check if executive summary exists
            has_exec_summary = any(
                "executive" in title.lower() and "summary" in title.lower()
                for title in sections.keys()
            )
            
            if not has_exec_summary and volume_num == 1:  # Add exec summary to Volume 1
                exec_summary = await self._generate_executive_summary(volume_data, proposal_state)
                
                # Insert at the beginning
                new_sections = {"Executive Summary": exec_summary}
                new_sections.update(sections)
                volume_data["sections"] = new_sections
        
        return enhanced_volumes
    
    async def _generate_executive_summary(
        self,
        volume_data: Dict[str, Any],
        proposal_state: ProposalState
    ) -> Dict[str, Any]:
        """Generate an executive summary for the proposal"""
        
        # Collect key content from all sections
        all_content = ""
        win_themes = set()
        
        for section_data in volume_data.get("sections", {}).values():
            content = section_data.get("content", "")
            all_content += content[:500] + "\n"  # First 500 chars of each section
            win_themes.update(section_data.get("win_themes", []))
        
        system_prompt = f"""
        **Role:** Senior Proposal Executive Summary Writer
        
        **Task:** Write a compelling executive summary for this government proposal.
        
        **Client:** {proposal_state.client_short_name}
        **Opportunity:** {proposal_state.opportunity_id}
        
        **Guidelines:**
        1. Highlight key benefits and value proposition
        2. Demonstrate understanding of requirements
        3. Showcase unique qualifications
        4. Include win themes naturally
        5. Keep to 2-3 paragraphs, ~300 words
        6. Use persuasive, professional tone
        
        **Win Themes:** {list(win_themes)}
        
        **Output:** Write only the executive summary content, no headers or formatting.
        """
        
        user_prompt = f"""
        Write an executive summary based on this proposal content:
        
        <proposal_content>
        {all_content[:3000]}
        </proposal_content>
        
        Create a compelling executive summary that captures the essence of our proposal.
        """
        
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm.ainvoke(messages)
            summary_content = response.content.strip()
            
            return {
                "title": "Executive Summary",
                "content": summary_content,
                "word_count": len(summary_content.split()),
                "requirements_addressed": [],
                "win_themes": list(win_themes),
                "quality_score": 0.9,
                "formatting_applied": True,
                "generated_by_integration": True
            }
            
        except Exception as e:
            logger.error(f"Error generating executive summary: {e}")
            return {
                "title": "Executive Summary",
                "content": f"Executive summary for {proposal_state.client_short_name} proposal.",
                "word_count": 8,
                "requirements_addressed": [],
                "win_themes": [],
                "quality_score": 0.5,
                "formatting_applied": True,
                "generated_by_integration": True
            }
    
    async def _perform_cross_volume_integration(
        self,
        enhanced_volumes: Dict[int, Dict[str, Any]],
        proposal_state: ProposalState
    ) -> Dict[int, Dict[str, Any]]:
        """Perform final cross-volume integration and consistency checks"""
        
        integrated_volumes = enhanced_volumes.copy()
        
        # Update cross-references
        for volume_num, volume_data in integrated_volumes.items():
            for section_title, section_data in volume_data.get("sections", {}).items():
                content = section_data.get("content", "")
                
                # Update volume references
                updated_content = await self._update_cross_references(content, integrated_volumes)
                section_data["content"] = updated_content
        
        return integrated_volumes
    
    async def _update_cross_references(self, content: str, all_volumes: Dict[int, Dict[str, Any]]) -> str:
        """Update cross-references in content"""
        
        # This is a simplified version - in practice, you'd have more sophisticated reference handling
        import re
        
        # Update volume references
        def replace_volume_ref(match):
            vol_num = int(match.group(1))
            if vol_num in all_volumes:
                return f"Volume {vol_num}"
            else:
                return f"[Volume {vol_num} - Not Available]"
        
        updated_content = re.sub(r'[Vv]olume\s+(\d+)', replace_volume_ref, content)
        
        return updated_content
    
    async def _add_cover_pages_and_front_matter(
        self,
        integrated_volumes: Dict[int, Dict[str, Any]],
        proposal_state: ProposalState
    ) -> Dict[int, Dict[str, Any]]:
        """Add cover pages and front matter to volumes"""
        
        final_volumes = {}
        
        for volume_num, volume_data in integrated_volumes.items():
            # Create cover page
            cover_page = await self._generate_cover_page(volume_num, proposal_state)
            
            # Create front matter
            front_matter = await self._generate_front_matter(volume_num, volume_data, proposal_state)
            
            # Assemble final volume
            final_volume = {
                "volume_number": volume_num,
                "cover_page": cover_page,
                "front_matter": front_matter,
                "table_of_contents": volume_data.get("table_of_contents", []),
                "sections": volume_data.get("sections", {}),
                "metadata": volume_data.get("metadata", {}),
                "integration_complete": True
            }
            
            final_volumes[volume_num] = final_volume
        
        return final_volumes
    
    async def _generate_cover_page(self, volume_num: int, proposal_state: ProposalState) -> Dict[str, Any]:
        """Generate cover page for volume"""
        
        volume_titles = {
            1: "Technical Volume",
            2: "Cost Volume", 
            3: "Past Performance Volume",
            4: "Small Business Volume",
            5: "Compliance Volume"
        }
        
        return {
            "title": volume_titles.get(volume_num, f"Volume {volume_num}"),
            "subtitle": f"Response to {proposal_state.opportunity_id}",
            "client": proposal_state.client_short_name,
            "submitted_by": "Kontratar AI",
            "submission_date": datetime.utcnow().strftime("%B %d, %Y"),
            "volume_number": volume_num
        }
    
    async def _generate_front_matter(
        self,
        volume_num: int,
        volume_data: Dict[str, Any],
        proposal_state: ProposalState
    ) -> Dict[str, Any]:
        """Generate front matter for volume"""
        
        return {
            "compliance_matrix": "Compliance matrix would go here",
            "acronyms_and_abbreviations": "Acronyms list would go here",
            "volume_overview": f"This volume contains our response for Volume {volume_num}",
            "page_count": volume_data.get("metadata", {}).get("page_count", 0),
            "word_count": volume_data.get("metadata", {}).get("word_count", 0)
        }
    
    async def _encrypt_and_store_volumes(
        self,
        final_volumes: Dict[int, Dict[str, Any]],
        proposal_state: ProposalState
    ) -> Dict[str, Any]:
        """Encrypt and store volumes in the database"""
        
        storage_result = {
            "stored_volumes": [],
            "storage_errors": [],
            "total_size_bytes": 0
        }
        
        logger.info(f"🔄 Starting storage process for {len(final_volumes)} volumes...")

        async for db in get_customer_db():
            logger.info(f"📊 Database connection established")

            for volume_num, volume_data in final_volumes.items():
                try:
                    logger.info(f"💾 Processing Volume {volume_num} for storage...")

                    # Convert to JSON bytes
                    volume_json = json.dumps(volume_data, indent=2)
                    volume_bytes = volume_json.encode('utf-8')
                    logger.info(f"   📄 Volume {volume_num} JSON size: {len(volume_bytes)} bytes")

                    # Encrypt the data
                    logger.info(f"   🔐 Encrypting Volume {volume_num}...")
                    encrypted_data = await self.proposal_decoding_service.encrypt_with_public_key(
                        db, proposal_state.tenant_id, volume_bytes
                    )
                    logger.info(f"   ✅ Volume {volume_num} encrypted successfully, size: {len(encrypted_data)} bytes")

                    # Store in database
                    logger.info(f"   💾 Storing Volume {volume_num} in database...")
                    review_record = await ProposalsInReviewController.add(
                        db=db,
                        client_short_name=proposal_state.client_short_name,
                        tenant_id=proposal_state.tenant_id,
                        section_number=str(volume_num),
                        opps_id=proposal_state.opportunity_id,
                        volume_number=volume_num,
                        version=1,
                        opps_type=proposal_state.opportunity_type.upper(),
                        proposal_data=encrypted_data,
                        job_instruction=json.dumps(proposal_state.metadata)
                    )

                    if review_record:
                        logger.info(f"   ✅ Volume {volume_num} stored successfully with ID: {review_record.id}")
                    else:
                        logger.error(f"   ❌ Volume {volume_num} storage returned None!")
                        raise Exception(f"ProposalsInReviewController.add returned None for volume {volume_num}")
                    
                    storage_result["stored_volumes"].append({
                        "volume_number": volume_num,
                        "record_id": review_record.id if review_record else None,
                        "size_bytes": len(volume_bytes),
                        "encrypted_size_bytes": len(encrypted_data)
                    })

                    storage_result["total_size_bytes"] += len(volume_bytes)
                    logger.info(f"   📊 Volume {volume_num} added to storage result")

                except Exception as e:
                    logger.error(f"❌ Error storing volume {volume_num}: {e}")
                    import traceback
                    logger.error(f"   Traceback: {traceback.format_exc()}")
                    storage_result["storage_errors"].append({
                        "volume_number": volume_num,
                        "error": str(e)
                    })
            
            break  # Exit after first database session

        # Log storage summary
        logger.info("📊 STORAGE SUMMARY:")
        logger.info(f"   ✅ Volumes stored: {len(storage_result['stored_volumes'])}")
        logger.info(f"   ❌ Storage errors: {len(storage_result['storage_errors'])}")
        logger.info(f"   📄 Total size: {storage_result['total_size_bytes']} bytes")

        if storage_result["storage_errors"]:
            logger.error("❌ STORAGE ERRORS DETECTED:")
            for error in storage_result["storage_errors"]:
                logger.error(f"   Volume {error['volume_number']}: {error['error']}")
        else:
            logger.info("✅ All volumes stored successfully!")

        return storage_result
    
    async def _generate_delivery_package(
        self,
        final_volumes: Dict[int, Dict[str, Any]],
        proposal_state: ProposalState,
        storage_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate final delivery package information"""
        
        return {
            "proposal_id": proposal_state.proposal_id,
            "opportunity_id": proposal_state.opportunity_id,
            "client": proposal_state.client_short_name,
            "volumes_included": list(final_volumes.keys()),
            "total_volumes": len(final_volumes),
            "total_sections": sum(len(vol.get("sections", {})) for vol in final_volumes.values()),
            "total_word_count": sum(vol.get("metadata", {}).get("word_count", 0) for vol in final_volumes.values()),
            "storage_summary": storage_result,
            "delivery_timestamp": datetime.utcnow().isoformat(),
            "ready_for_review": len(storage_result["storage_errors"]) == 0,
            "quality_summary": {
                "average_quality": sum(vol.get("metadata", {}).get("quality_score", 0) for vol in final_volumes.values()) / len(final_volumes),
                "average_compliance": sum(vol.get("metadata", {}).get("compliance_score", 0) for vol in final_volumes.values()) / len(final_volumes)
            }
        }
    
    async def validate_input(self, proposal_state: ProposalState, context: Dict[str, Any]) -> bool:
        """Validate that required inputs are available"""
        return proposal_state is not None and len(proposal_state.volumes) > 0
    
    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """Validate the agent's output"""
        required_keys = ["status", "delivery_package"]
        return all(key in output for key in required_keys)
