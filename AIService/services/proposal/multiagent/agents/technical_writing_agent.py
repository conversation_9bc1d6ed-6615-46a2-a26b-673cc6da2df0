"""
Technical Writing Agent for Multi-Agent Proposal Generation.
Specializes in generating high-quality proposal content for specific volumes.
"""

import json
from typing import Any, Dict, List, Optional
from langchain_ollama import <PERSON>t<PERSON>lla<PERSON>

from loguru import logger

from ..core import (
    BaseAgent, AgentCapability, AgentCommunication, 
    ProposalState, ContextManager, ContextType
)


class TechnicalWritingAgent(BaseAgent):
    """
    Specialized agent for technical writing and content generation.
    Can be specialized for different volume types (technical, cost, past performance, etc.)
    """
    
    def __init__(
        self,
        communication: AgentCommunication,
        context_manager: ContextManager,
        volume_specialization: int = 1,
        llm_api_url: str = "http://ai.kontratar.com:11434"
    ):
        super().__init__(
            name=f"Technical Writing Agent (Volume {volume_specialization})",
            capabilities=[
                AgentCapability.TECHNICAL_WRITING,
                AgentCapability.RESEARCH,
                AgentCapability.VALIDATION
            ],
            communication=communication
        )
        
        self.context_manager = context_manager
        self.volume_specialization = volume_specialization
        self.llm = ChatOllama(
            model="gemma3:27b",
            num_ctx=8000,
            temperature=0.3,
            base_url=llm_api_url
        )
        
        # Volume-specific configurations
        self.volume_configs = {
            1: {
                "focus": "technical_approach",
                "tone": "professional_technical",
                "key_sections": ["Executive Summary", "Technical Approach", "Management Plan", "Staffing Plan"]
            },
            2: {
                "focus": "cost_analysis",
                "tone": "analytical_precise",
                "key_sections": ["Cost Summary", "Basis of Estimate", "Cost Breakdown"]
            },
            3: {
                "focus": "past_performance",
                "tone": "evidence_based",
                "key_sections": ["Past Performance Matrix", "Reference Details", "Lessons Learned"]
            },
            4: {
                "focus": "small_business",
                "tone": "commitment_focused",
                "key_sections": ["Small Business Plan", "Subcontracting Plan", "Outreach Strategy"]
            },
            5: {
                "focus": "compliance_certifications",
                "tone": "compliance_focused",
                "key_sections": ["Certifications", "Compliance Matrix", "Security Clearances"]
            }
        }
        
        logger.info(f"Initialized {self.name}")

    def _get_requirement_attr(self, req, attr_name: str, default=None):
        """Safely get attribute from RequirementItem object or dict"""
        if hasattr(req, attr_name):
            # It's a RequirementItem object
            return getattr(req, attr_name, default)
        elif isinstance(req, dict):
            # It's a dictionary
            return req.get(attr_name, default)
        else:
            return default

    async def execute(self, proposal_state: ProposalState, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute content generation for the assigned volume.
        
        Returns:
            Dict containing generated sections and metadata
        """
        
        volume_number = context["volume_number"]
        opportunity_id = context["opportunity_id"]
        tenant_id = context["tenant_id"]
        source = context["source"]
        client_short_name = context["client_short_name"]
        requirements = context.get("requirements", [])
        content_compliance = context.get("content_compliance", {})
        
        logger.info(f"Starting content generation for Volume {volume_number}")
        
        # Step 1: Generate table of contents
        table_of_contents = await self._generate_table_of_contents(
            volume_number, opportunity_id, tenant_id, source, content_compliance
        )
        
        # Step 2: Generate detailed outline
        detailed_outline = await self._generate_detailed_outline(
            table_of_contents, volume_number, opportunity_id, tenant_id, source, requirements
        )
        
        # Step 3: Generate content for each section
        sections = await self._generate_section_content(
            detailed_outline, volume_number, opportunity_id, tenant_id, source, 
            client_short_name, requirements, content_compliance
        )
        
        # Step 4: Perform internal quality check
        quality_check = await self._perform_quality_check(sections, volume_number, requirements)
        
        result = {
            "volume_number": volume_number,
            "table_of_contents": table_of_contents,
            "detailed_outline": detailed_outline,
            "sections": sections,
            "quality_check": quality_check,
            "metadata": {
                "total_sections": len(sections),
                "estimated_word_count": sum(len(section.get("content", "").split()) for section in sections.values()),
                "generation_timestamp": proposal_state.last_updated.isoformat()
            }
        }
        
        logger.info(f"Content generation completed for Volume {volume_number}: {len(sections)} sections")
        return result
    
    async def _generate_table_of_contents(
        self,
        volume_number: int,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        content_compliance: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate table of contents for the volume"""
        
        # Get volume-specific guidance
        volume_guidance = content_compliance.get("volume_guidance", {}).get(volume_number, {})
        required_sections = volume_guidance.get("content_requirements", [])
        
        # Get context for TOC generation
        context_items = await self.context_manager.get_context(
            query=f"""
            Table of contents requirements for Volume {volume_number}, 
            required sections, section organization, proposal structure,
            evaluation criteria for volume {volume_number}.
            """,
            context_types=[ContextType.OPPORTUNITY, ContextType.COMPLIANCE],
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            max_chunks_per_type=5
        )
        
        context_text = self.context_manager.format_context_for_llm(context_items, max_length=6000)
        
        # Get volume configuration
        config = self.volume_configs.get(volume_number, self.volume_configs[1])
        
        system_prompt = f"""
        **Role:** Government Proposal Structure Expert for Volume {volume_number}
        
        **Task:** Generate a comprehensive table of contents for Volume {volume_number} ({config['focus']}).
        
        **Volume Focus:** {config['focus']}
        **Required Sections:** {required_sections}
        **Key Sections:** {config['key_sections']}
        
        **Instructions:**
        1. Create a logical, hierarchical structure
        2. Include all required sections from the RFP
        3. Add appropriate subsections for comprehensive coverage
        4. Ensure alignment with evaluation criteria
        5. Follow government proposal best practices
        
        **Output Format:**
        Return a JSON array of sections with this structure:
        [
            {{
                "section_number": "1.0",
                "title": "Executive Summary",
                "description": "Overview of our approach and key benefits",
                "subsections": [
                    {{
                        "section_number": "1.1",
                        "title": "Understanding of Requirements",
                        "description": "Demonstration of requirement comprehension"
                    }}
                ],
                "page_estimate": 3,
                "evaluation_weight": "High"
            }}
        ]
        """
        
        user_prompt = f"""
        Generate table of contents for Volume {volume_number} based on these requirements:
        
        <requirements>
        Required Sections: {required_sections}
        Volume Guidance: {volume_guidance}
        </requirements>
        
        <rfp_context>
        {context_text}
        </rfp_context>
        
        Return ONLY the JSON array of sections.
        """
        
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # Parse JSON response
            toc_json = response.content.strip()
            if toc_json.startswith("```json"):
                toc_json = toc_json.split("```json")[1].split("```")[0]
            elif toc_json.startswith("```"):
                toc_json = toc_json.split("```")[1].split("```")[0]
            
            table_of_contents = json.loads(toc_json)
            
            if not isinstance(table_of_contents, list):
                return []
            
            return table_of_contents
            
        except Exception as e:
            logger.error(f"Error generating table of contents: {e}")
            # Return default structure based on volume type
            return self._get_default_toc(volume_number)
    
    async def _generate_detailed_outline(
        self,
        table_of_contents: List[Dict[str, Any]],
        volume_number: int,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        requirements: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Generate detailed outline for each section"""
        
        detailed_outline = []
        
        for section in table_of_contents:
            section_title = section.get("title", "")
            section_description = section.get("description", "")
            
            # Get specialized context for this section
            specialized_context = await self.context_manager.get_specialized_context(
                section_title=section_title,
                section_description=section_description,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                source=source,
                max_chunks=8
            )
            
            # Generate outline for this section
            section_outline = await self._generate_section_outline(
                section, specialized_context, requirements, volume_number
            )
            
            detailed_outline.append(section_outline)
        
        return detailed_outline
    
    async def _generate_section_outline(
        self,
        section: Dict[str, Any],
        specialized_context: Dict[str, List],
        requirements: List,  # Can be RequirementItem objects or dicts
        volume_number: int
    ) -> Dict[str, Any]:
        """Generate detailed outline for a specific section"""
        
        section_title = section.get("title", "")
        section_description = section.get("description", "")
        
        # Filter relevant requirements
        relevant_requirements = [
            req for req in requirements
            if (self._get_requirement_attr(req, 'assigned_volume') == volume_number) or
            any(keyword in section_title.lower() for keyword in self._get_requirement_attr(req, 'keywords', []))
        ]
        
        # Format context
        context_text = ""
        for context_type, items in specialized_context.items():
            if items:
                context_text += f"\n## {context_type.title()} Context:\n"
                for item in items[:3]:  # Limit to top 3 items per type
                    context_text += f"- {item.content[:200]}...\n"
        
        system_prompt = f"""
        **Role:** Senior Proposal Writer specializing in Volume {volume_number}
        
        **Task:** Create a detailed outline for the section "{section_title}".
        
        **Section Description:** {section_description}
        
        **Instructions:**
        1. Create a comprehensive content outline
        2. Address all relevant requirements
        3. Include specific talking points and key messages
        4. Suggest supporting evidence and examples
        5. Identify opportunities for win themes
        6. Note any graphics or tables needed
        
        **Output Format:**
        Return a JSON object with this structure:
        {{
            "section_title": "{section_title}",
            "content_outline": [
                "Key point 1 with specific details",
                "Key point 2 addressing requirement X",
                "Supporting evidence and examples"
            ],
            "win_themes": ["Theme 1", "Theme 2"],
            "requirements_addressed": ["REQ_001", "REQ_002"],
            "supporting_materials": ["Table of capabilities", "Process diagram"],
            "key_messages": ["Message 1", "Message 2"],
            "word_count_target": 500
        }}
        """
        
        user_prompt = f"""
        Create detailed outline for section "{section_title}":
        
        <section_info>
        Description: {section_description}
        </section_info>
        
        <relevant_requirements>
        {json.dumps([req for req in relevant_requirements[:5]], indent=2)}
        </relevant_requirements>
        
        <context>
        {context_text}
        </context>
        
        Return ONLY the JSON object.
        """
        
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm.ainvoke(messages)
            
            # Parse JSON response
            outline_json = response.content.strip()
            if outline_json.startswith("```json"):
                outline_json = outline_json.split("```json")[1].split("```")[0]
            elif outline_json.startswith("```"):
                outline_json = outline_json.split("```")[1].split("```")[0]
            
            section_outline = json.loads(outline_json)
            return section_outline
            
        except Exception as e:
            logger.error(f"Error generating section outline for {section_title}: {e}")
            return {
                "section_title": section_title,
                "content_outline": [f"Content for {section_title}"],
                "win_themes": [],
                "requirements_addressed": [],
                "supporting_materials": [],
                "key_messages": [],
                "word_count_target": 300
            }
    
    async def _generate_section_content(
        self,
        detailed_outline: List[Dict[str, Any]],
        volume_number: int,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: str,
        requirements: List[Dict[str, Any]],
        content_compliance: Dict[str, Any]
    ) -> Dict[str, Dict[str, Any]]:
        """Generate actual content for each section"""
        
        sections = {}
        
        for outline in detailed_outline:
            section_title = outline.get("section_title", "")
            
            try:
                # Generate content for this section
                section_content = await self._generate_individual_section_content(
                    outline, volume_number, opportunity_id, tenant_id, source,
                    client_short_name, requirements, content_compliance
                )
                
                sections[section_title] = section_content
                
            except Exception as e:
                logger.error(f"Error generating content for section {section_title}: {e}")
                sections[section_title] = {
                    "title": section_title,
                    "content": f"[Content generation failed for {section_title}]",
                    "word_count": 0,
                    "requirements_addressed": [],
                    "quality_score": 0.0
                }
        
        return sections
    
    async def _generate_individual_section_content(
        self,
        outline: Dict[str, Any],
        volume_number: int,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        client_short_name: str,
        requirements: List,  # Can be RequirementItem objects or dicts
        content_compliance: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate content for an individual section"""
        
        section_title = outline.get("section_title", "")
        content_outline = outline.get("content_outline", [])
        win_themes = outline.get("win_themes", [])
        requirements_addressed = outline.get("requirements_addressed", [])
        
        # Get enhanced context for content generation
        context_items = await self.context_manager.get_specialized_context(
            section_title=section_title,
            section_description=" ".join(content_outline),
            opportunity_id=opportunity_id,
            tenant_id=tenant_id,
            source=source,
            client_short_name=client_short_name,
            max_chunks=10
        )
        
        # Format context for LLM
        context_text = ""
        for context_type, items in context_items.items():
            if items:
                context_text += f"\n## {context_type.title()} Information:\n"
                for item in items[:4]:
                    context_text += f"- {item.content[:300]}...\n"
        
        # Get volume configuration
        config = self.volume_configs.get(volume_number, self.volume_configs[1])
        
        system_prompt = f"""
        **Role:** Expert Government Proposal Writer for {client_short_name}
        
        **Task:** Write compelling, compliant content for the "{section_title}" section of Volume {volume_number}.
        
        **Writing Guidelines:**
        - Tone: {config['tone']}
        - Focus: {config['focus']}
        - Target Length: {outline.get('word_count_target', 500)} words
        - Win Themes: {win_themes}
        
        **Requirements to Address:** {requirements_addressed}
        
        **Instructions:**
        1. Write professional, persuasive content
        2. Address all specified requirements
        3. Incorporate win themes naturally
        4. Use specific examples and evidence
        5. Maintain government proposal standards
        6. Include quantifiable benefits where possible
        7. Use active voice and clear language
        
        **Output Format:**
        Write the complete section content in professional proposal format.
        Use proper headings, bullet points, and formatting as appropriate.
        """
        
        user_prompt = f"""
        Write content for the "{section_title}" section based on this outline:
        
        <outline>
        {json.dumps(outline, indent=2)}
        </outline>
        
        <context_information>
        {context_text}
        </context_information>
        
        <client_information>
        Client: {client_short_name}
        Opportunity: {opportunity_id}
        </client_information>
        
        Write compelling, compliant proposal content that addresses all requirements and incorporates the win themes.
        """
        
        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
            
            response = await self.llm.ainvoke(messages)
            content = response.content.strip()
            
            # Calculate metrics
            word_count = len(content.split())
            
            return {
                "title": section_title,
                "content": content,
                "word_count": word_count,
                "requirements_addressed": requirements_addressed,
                "win_themes": win_themes,
                "outline": outline,
                "quality_score": 0.8  # Will be updated by QA agent
            }
            
        except Exception as e:
            logger.error(f"Error generating content for {section_title}: {e}")
            raise
    
    async def _perform_quality_check(
        self,
        sections: Dict[str, Dict[str, Any]],
        volume_number: int,
        requirements: List  # Can be RequirementItem objects or dicts
    ) -> Dict[str, Any]:
        """Perform internal quality check on generated content"""
        
        quality_check = {
            "total_sections": len(sections),
            "total_word_count": sum(section.get("word_count", 0) for section in sections.values()),
            "requirements_coverage": {},
            "issues": [],
            "recommendations": []
        }
        
        # Check requirement coverage
        volume_requirements = [
            req for req in requirements
            if self._get_requirement_attr(req, 'assigned_volume') == volume_number
        ]
        covered_requirements = set()

        for section in sections.values():
            covered_requirements.update(section.get("requirements_addressed", []))

        uncovered_requirements = [
            self._get_requirement_attr(req, 'requirement_id', self._get_requirement_attr(req, 'id', 'unknown'))
            for req in volume_requirements
            if self._get_requirement_attr(req, 'requirement_id', self._get_requirement_attr(req, 'id', 'unknown')) not in covered_requirements
        ]
        
        quality_check["requirements_coverage"] = {
            "total_requirements": len(volume_requirements),
            "covered_requirements": len(covered_requirements),
            "uncovered_requirements": uncovered_requirements,
            "coverage_percentage": (len(covered_requirements) / len(volume_requirements) * 100) if volume_requirements else 100
        }
        
        # Check for issues
        if uncovered_requirements:
            quality_check["issues"].append(f"Uncovered requirements: {uncovered_requirements}")
            quality_check["recommendations"].append("Review and address uncovered requirements")
        
        if quality_check["total_word_count"] < 1000:
            quality_check["issues"].append("Content may be too brief")
            quality_check["recommendations"].append("Consider expanding content with more detail")
        
        return quality_check
    
    def _get_default_toc(self, volume_number: int) -> List[Dict[str, Any]]:
        """Get default table of contents for volume type"""
        
        default_tocs = {
            1: [
                {"section_number": "1.0", "title": "Executive Summary", "description": "Overview of approach", "page_estimate": 3},
                {"section_number": "2.0", "title": "Technical Approach", "description": "Technical methodology", "page_estimate": 15},
                {"section_number": "3.0", "title": "Management Plan", "description": "Project management approach", "page_estimate": 10}
            ],
            2: [
                {"section_number": "1.0", "title": "Cost Summary", "description": "Total cost breakdown", "page_estimate": 2},
                {"section_number": "2.0", "title": "Basis of Estimate", "description": "Cost estimation methodology", "page_estimate": 5}
            ],
            3: [
                {"section_number": "1.0", "title": "Past Performance Matrix", "description": "Relevant contract experience", "page_estimate": 5},
                {"section_number": "2.0", "title": "Reference Details", "description": "Detailed reference information", "page_estimate": 10}
            ]
        }
        
        return default_tocs.get(volume_number, default_tocs[1])

    async def validate_input(self, proposal_state: ProposalState, context: Dict[str, Any]) -> bool:
        """Validate that required inputs are available"""
        required_fields = ["volume_number", "opportunity_id", "tenant_id", "source", "client_short_name"]
        return all(field in context for field in required_fields)

    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """Validate the agent's output"""
        required_keys = ["volume_number", "sections", "quality_check"]
        return all(key in output for key in required_keys)
