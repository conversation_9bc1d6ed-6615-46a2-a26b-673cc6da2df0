"""
Quality Assurance Agent for Multi-Agent Proposal Generation.
Specializes in quality assessment, compliance validation, and content review.
"""

import json
from typing import Any, Dict, List, Optional
from langchain_ollama import ChatOllama

from loguru import logger

from ..core import (
    BaseAgent, AgentCapability, AgentCommunication, 
    ProposalState, QualityMetrics, ComplianceMetrics,
    VolumeStatus, MetricType
)


class QualityAssuranceAgent(BaseAgent):
    """
    Specialized agent for comprehensive quality assurance.
    Validates content quality, compliance, and consistency across volumes.
    """
    
    def __init__(
        self,
        communication: AgentCommunication,
        quality_metrics: QualityMetrics,
        compliance_metrics: ComplianceMetrics,
        llm_api_url: str = "http://ai.kontratar.com:11434"
    ):
        super().__init__(
            name="Quality Assurance Agent",
            capabilities=[
                AgentCapability.QUALITY_ASSURANCE,
                AgentCapability.VALIDATION,
                AgentCapability.RESEARCH
            ],
            communication=communication
        )
        
        self.quality_metrics = quality_metrics
        self.compliance_metrics = compliance_metrics
        self.llm = ChatOllama(
            model="gemma3:27b",
            num_ctx=8000,
            temperature=0.0,
            base_url=llm_api_url
        )
        
        logger.info(f"Initialized {self.name}")
    
    async def execute(self, proposal_state: ProposalState, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute comprehensive quality assurance for all volumes.
        
        Returns:
            Dict containing quality scores, compliance assessment, and recommendations
        """
        
        quality_threshold = context.get("quality_threshold", 0.8)
        compliance_threshold = context.get("compliance_threshold", 0.9)
        
        logger.info(f"Starting QA for proposal {proposal_state.proposal_id}")
        
        # Step 1: Assess quality for each volume
        volume_quality_scores = await self._assess_volume_quality(proposal_state)
        
        # Step 2: Validate compliance across all volumes
        compliance_assessment = await self._validate_compliance(proposal_state)
        
        # Step 3: Check cross-volume consistency
        consistency_check = await self._check_cross_volume_consistency(proposal_state)
        
        # Step 4: Validate requirement coverage
        requirement_coverage = await self._validate_requirement_coverage(proposal_state)
        
        # Step 5: Generate improvement recommendations
        recommendations = await self._generate_recommendations(
            volume_quality_scores, compliance_assessment, consistency_check,
            requirement_coverage, quality_threshold, compliance_threshold
        )
        
        # Step 6: Calculate overall scores
        overall_scores = await self._calculate_overall_scores(
            volume_quality_scores, compliance_assessment, consistency_check
        )
        
        # Step 7: Determine pass/fail status
        qa_status = await self._determine_qa_status(
            overall_scores, quality_threshold, compliance_threshold
        )
        
        result = {
            "quality_scores": volume_quality_scores,
            "compliance_assessment": compliance_assessment,
            "consistency_check": consistency_check,
            "requirement_coverage": requirement_coverage,
            "recommendations": recommendations,
            "overall_scores": overall_scores,
            "qa_status": qa_status,
            "thresholds": {
                "quality_threshold": quality_threshold,
                "compliance_threshold": compliance_threshold
            }
        }
        
        # Update proposal state with QA results
        await self._update_proposal_state_with_qa_results(proposal_state, result)
        
        logger.info(f"QA completed for proposal {proposal_state.proposal_id}: {qa_status['status']}")
        return result
    
    async def _assess_volume_quality(self, proposal_state: ProposalState) -> Dict[int, Dict[str, Any]]:
        """Assess quality for each volume"""
        
        volume_quality_scores = {}
        
        for volume_num, volume_state in proposal_state.volumes.items():
            if volume_state.status != VolumeStatus.COMPLETED:
                continue
            
            volume_scores = {}
            volume_issues = []
            volume_recommendations = []
            
            # Assess each section in the volume
            for section_title, section_data in volume_state.sections.items():
                content = section_data.get("content", "")
                
                if not content or content.strip() == "":
                    volume_issues.append(f"Section '{section_title}' has no content")
                    continue
                
                # Calculate quality metrics
                quality_result = self.quality_metrics.calculate_quality_score(
                    content=content,
                    section_title=section_title,
                    volume_number=volume_num
                )
                
                volume_scores[section_title] = {
                    "quality_score": quality_result.score,
                    "details": quality_result.details,
                    "issues": quality_result.issues,
                    "recommendations": quality_result.recommendations
                }
                
                volume_issues.extend(quality_result.issues)
                volume_recommendations.extend(quality_result.recommendations)
            
            # Calculate volume average
            if volume_scores:
                avg_quality = sum(s["quality_score"] for s in volume_scores.values()) / len(volume_scores)
            else:
                avg_quality = 0.0
            
            volume_quality_scores[volume_num] = {
                "average_quality": avg_quality,
                "section_scores": volume_scores,
                "total_sections": len(volume_scores),
                "issues": volume_issues,
                "recommendations": volume_recommendations,
                "word_count": volume_state.word_count,
                "estimated_pages": volume_state.word_count / 250  # Rough estimate
            }
            
            # Update volume state
            volume_state.quality_score = avg_quality
        
        return volume_quality_scores
    
    async def _validate_compliance(self, proposal_state: ProposalState) -> Dict[str, Any]:
        """Validate compliance across all volumes"""
        
        compliance_assessment = {
            "overall_compliance_score": 0.0,
            "volume_compliance": {},
            "regulatory_compliance": {},
            "format_compliance": {},
            "requirement_compliance": {},
            "critical_issues": [],
            "warnings": []
        }
        
        # Get all requirements for compliance checking
        requirements = [req.description for req in proposal_state.requirements]
        
        volume_compliance_scores = []
        
        for volume_num, volume_state in proposal_state.volumes.items():
            if volume_state.status != VolumeStatus.COMPLETED:
                continue
            
            # Combine all content for the volume
            volume_content = ""
            for section_data in volume_state.sections.values():
                volume_content += section_data.get("content", "") + "\n\n"
            
            if not volume_content.strip():
                compliance_assessment["critical_issues"].append(f"Volume {volume_num} has no content")
                continue
            
            # Calculate compliance score
            compliance_result = self.compliance_metrics.calculate_compliance_score(
                content=volume_content,
                requirements=requirements,
                volume_number=volume_num
            )
            
            compliance_assessment["volume_compliance"][volume_num] = {
                "compliance_score": compliance_result.score,
                "details": compliance_result.details,
                "issues": compliance_result.issues,
                "recommendations": compliance_result.recommendations
            }
            
            volume_compliance_scores.append(compliance_result.score)
            
            # Update volume state
            volume_state.compliance_score = compliance_result.score
            
            # Collect critical issues
            for issue in compliance_result.issues:
                if "critical" in issue.lower() or "mandatory" in issue.lower():
                    compliance_assessment["critical_issues"].append(f"Volume {volume_num}: {issue}")
                else:
                    compliance_assessment["warnings"].append(f"Volume {volume_num}: {issue}")
        
        # Calculate overall compliance score
        if volume_compliance_scores:
            compliance_assessment["overall_compliance_score"] = sum(volume_compliance_scores) / len(volume_compliance_scores)
        
        return compliance_assessment
    
    async def _check_cross_volume_consistency(self, proposal_state: ProposalState) -> Dict[str, Any]:
        """Check consistency across volumes"""
        
        consistency_check = {
            "consistency_score": 1.0,
            "terminology_consistency": {},
            "cross_references": {},
            "style_consistency": {},
            "issues": [],
            "recommendations": []
        }
        
        # Collect all content for analysis
        all_content = {}
        for volume_num, volume_state in proposal_state.volumes.items():
            if volume_state.status == VolumeStatus.COMPLETED:
                volume_content = ""
                for section_data in volume_state.sections.values():
                    volume_content += section_data.get("content", "") + " "
                all_content[volume_num] = volume_content
        
        if len(all_content) < 2:
            return consistency_check
        
        # Check terminology consistency
        terminology_issues = await self._check_terminology_consistency(all_content)
        consistency_check["terminology_consistency"] = terminology_issues
        
        # Check cross-references
        cross_ref_issues = await self._check_cross_references(all_content)
        consistency_check["cross_references"] = cross_ref_issues
        
        # Calculate consistency score
        issue_count = len(terminology_issues.get("issues", [])) + len(cross_ref_issues.get("issues", []))
        if issue_count > 0:
            consistency_check["consistency_score"] = max(0.0, 1.0 - (issue_count * 0.1))
        
        consistency_check["issues"] = terminology_issues.get("issues", []) + cross_ref_issues.get("issues", [])
        consistency_check["recommendations"] = terminology_issues.get("recommendations", []) + cross_ref_issues.get("recommendations", [])
        
        return consistency_check
    
    async def _check_terminology_consistency(self, all_content: Dict[int, str]) -> Dict[str, Any]:
        """Check for consistent terminology across volumes"""
        
        # This is a simplified version - in practice, you'd use more sophisticated NLP
        terminology_check = {
            "issues": [],
            "recommendations": [],
            "inconsistent_terms": []
        }
        
        # Look for common inconsistencies
        common_variations = [
            ["email", "e-mail", "Email", "E-mail"],
            ["website", "web site", "Website", "Web site"],
            ["online", "on-line", "Online", "On-line"],
            ["setup", "set up", "Setup", "Set up"]
        ]
        
        for variations in common_variations:
            found_variations = []
            for volume_num, content in all_content.items():
                for variation in variations:
                    if variation in content:
                        found_variations.append((volume_num, variation))
            
            if len(set(var[1] for var in found_variations)) > 1:
                terminology_check["issues"].append(f"Inconsistent terminology: {variations}")
                terminology_check["inconsistent_terms"].append(variations[0])
                terminology_check["recommendations"].append(f"Standardize usage of '{variations[0]}'")
        
        return terminology_check
    
    async def _check_cross_references(self, all_content: Dict[int, str]) -> Dict[str, Any]:
        """Check cross-references between volumes"""
        
        cross_ref_check = {
            "issues": [],
            "recommendations": [],
            "broken_references": []
        }
        
        # Look for volume references
        import re
        
        for volume_num, content in all_content.items():
            # Find references to other volumes
            volume_refs = re.findall(r'[Vv]olume\s+(\d+)', content)
            section_refs = re.findall(r'[Ss]ection\s+(\d+(?:\.\d+)*)', content)
            
            for ref in volume_refs:
                ref_num = int(ref)
                if ref_num not in all_content and ref_num != volume_num:
                    cross_ref_check["issues"].append(f"Volume {volume_num} references non-existent Volume {ref_num}")
                    cross_ref_check["broken_references"].append(f"Volume {ref_num}")
        
        if cross_ref_check["broken_references"]:
            cross_ref_check["recommendations"].append("Fix or remove broken volume references")
        
        return cross_ref_check
    
    async def _validate_requirement_coverage(self, proposal_state: ProposalState) -> Dict[str, Any]:
        """Validate that all requirements are adequately covered"""
        
        coverage_analysis = {
            "total_requirements": len(proposal_state.requirements),
            "covered_requirements": 0,
            "uncovered_requirements": [],
            "partially_covered": [],
            "coverage_percentage": 0.0,
            "critical_gaps": []
        }
        
        # Check each requirement
        for requirement in proposal_state.requirements:
            req_id = requirement.requirement_id
            req_description = requirement.description
            req_priority = requirement.priority
            
            # Check if requirement is addressed in any volume
            is_covered = False
            coverage_quality = 0.0
            
            for volume_state in proposal_state.volumes.values():
                if volume_state.status != VolumeStatus.COMPLETED:
                    continue
                
                for section_data in volume_state.sections.values():
                    content = section_data.get("content", "")
                    requirements_addressed = section_data.get("requirements_addressed", [])
                    
                    if req_id in requirements_addressed:
                        is_covered = True
                        coverage_quality = 1.0
                        break
                    
                    # Simple keyword matching for partial coverage
                    req_keywords = req_description.lower().split()[:3]  # First 3 words
                    if any(keyword in content.lower() for keyword in req_keywords if len(keyword) > 3):
                        coverage_quality = max(coverage_quality, 0.5)
            
            if coverage_quality >= 0.8:
                coverage_analysis["covered_requirements"] += 1
            elif coverage_quality >= 0.3:
                coverage_analysis["partially_covered"].append({
                    "requirement_id": req_id,
                    "description": req_description,
                    "coverage_quality": coverage_quality
                })
            else:
                coverage_analysis["uncovered_requirements"].append({
                    "requirement_id": req_id,
                    "description": req_description,
                    "priority": req_priority
                })
                
                if req_priority in ["CRITICAL", "HIGH"]:
                    coverage_analysis["critical_gaps"].append(req_id)
        
        # Calculate coverage percentage
        if coverage_analysis["total_requirements"] > 0:
            coverage_analysis["coverage_percentage"] = (
                coverage_analysis["covered_requirements"] / coverage_analysis["total_requirements"] * 100
            )
        
        return coverage_analysis
    
    async def _generate_recommendations(
        self,
        volume_quality_scores: Dict[int, Dict[str, Any]],
        compliance_assessment: Dict[str, Any],
        consistency_check: Dict[str, Any],
        requirement_coverage: Dict[str, Any],
        quality_threshold: float,
        compliance_threshold: float
    ) -> List[Dict[str, Any]]:
        """Generate actionable recommendations for improvement"""
        
        recommendations = []
        
        # Quality recommendations
        for volume_num, quality_data in volume_quality_scores.items():
            if quality_data["average_quality"] < quality_threshold:
                recommendations.append({
                    "type": "quality",
                    "priority": "HIGH",
                    "volume": volume_num,
                    "issue": f"Volume {volume_num} quality score ({quality_data['average_quality']:.2f}) below threshold ({quality_threshold})",
                    "recommendation": f"Review and improve content quality for Volume {volume_num}",
                    "specific_actions": quality_data.get("recommendations", [])
                })
        
        # Compliance recommendations
        if compliance_assessment["overall_compliance_score"] < compliance_threshold:
            recommendations.append({
                "type": "compliance",
                "priority": "CRITICAL",
                "volume": "all",
                "issue": f"Overall compliance score ({compliance_assessment['overall_compliance_score']:.2f}) below threshold ({compliance_threshold})",
                "recommendation": "Address compliance issues across all volumes",
                "specific_actions": compliance_assessment.get("critical_issues", [])
            })
        
        # Requirement coverage recommendations
        if requirement_coverage["coverage_percentage"] < 90:
            recommendations.append({
                "type": "coverage",
                "priority": "HIGH",
                "volume": "multiple",
                "issue": f"Requirement coverage ({requirement_coverage['coverage_percentage']:.1f}%) is insufficient",
                "recommendation": "Address uncovered requirements",
                "specific_actions": [f"Cover requirement {req['requirement_id']}" for req in requirement_coverage["uncovered_requirements"][:5]]
            })
        
        # Consistency recommendations
        if consistency_check["consistency_score"] < 0.8:
            recommendations.append({
                "type": "consistency",
                "priority": "MEDIUM",
                "volume": "all",
                "issue": "Inconsistencies found across volumes",
                "recommendation": "Improve cross-volume consistency",
                "specific_actions": consistency_check.get("recommendations", [])
            })
        
        return recommendations
    
    async def _calculate_overall_scores(
        self,
        volume_quality_scores: Dict[int, Dict[str, Any]],
        compliance_assessment: Dict[str, Any],
        consistency_check: Dict[str, Any]
    ) -> Dict[str, float]:
        """Calculate overall scores for the proposal"""
        
        # Calculate average quality score
        quality_scores = [data["average_quality"] for data in volume_quality_scores.values()]
        overall_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0.0
        
        # Get compliance score
        overall_compliance = compliance_assessment["overall_compliance_score"]
        
        # Get consistency score
        overall_consistency = consistency_check["consistency_score"]
        
        # Calculate weighted overall score
        overall_score = (
            overall_quality * 0.4 +
            overall_compliance * 0.4 +
            overall_consistency * 0.2
        )
        
        return {
            "overall_quality": overall_quality,
            "overall_compliance": overall_compliance,
            "overall_consistency": overall_consistency,
            "weighted_overall": overall_score
        }
    
    async def _determine_qa_status(
        self,
        overall_scores: Dict[str, float],
        quality_threshold: float,
        compliance_threshold: float
    ) -> Dict[str, Any]:
        """Determine overall QA pass/fail status"""
        
        quality_pass = overall_scores["overall_quality"] >= quality_threshold
        compliance_pass = overall_scores["overall_compliance"] >= compliance_threshold
        consistency_pass = overall_scores["overall_consistency"] >= 0.8
        
        overall_pass = quality_pass and compliance_pass and consistency_pass
        
        return {
            "status": "PASS" if overall_pass else "FAIL",
            "quality_pass": quality_pass,
            "compliance_pass": compliance_pass,
            "consistency_pass": consistency_pass,
            "overall_score": overall_scores["weighted_overall"],
            "ready_for_delivery": overall_pass and overall_scores["weighted_overall"] >= 0.85
        }
    
    async def _update_proposal_state_with_qa_results(
        self,
        proposal_state: ProposalState,
        qa_results: Dict[str, Any]
    ):
        """Update proposal state with QA results"""
        
        # Update overall scores
        proposal_state.overall_quality_score = qa_results["overall_scores"]["overall_quality"]
        proposal_state.overall_compliance_score = qa_results["overall_scores"]["overall_compliance"]
        
        # Add any critical issues as errors
        for issue in qa_results["compliance_assessment"].get("critical_issues", []):
            proposal_state.add_error(f"QA Critical Issue: {issue}")
        
        # Add recommendations as warnings
        for rec in qa_results["recommendations"]:
            if rec["priority"] in ["HIGH", "CRITICAL"]:
                proposal_state.add_warning(f"QA Recommendation: {rec['recommendation']}")
    
    async def validate_input(self, proposal_state: ProposalState, context: Dict[str, Any]) -> bool:
        """Validate that required inputs are available"""
        return proposal_state is not None and len(proposal_state.volumes) > 0
    
    async def validate_output(self, output: Dict[str, Any]) -> bool:
        """Validate the agent's output"""
        required_keys = ["quality_scores", "compliance_assessment", "qa_status"]
        return all(key in output for key in required_keys)
