"""
Multi-Agent Pipeline Service for Proposal Generation.
Drop-in replacement for the legacy ProposalOutlineService that uses the multi-agent system.
Maintains compatibility with existing database storage and PDF export functionality.
"""

import asyncio
import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger

from .multiagent.multiagent_proposal_service import MultiAgentProposalService
from .multiagent.orchestrator import OrchestrationConfig
from .multiagent_database_integration import MultiAgentDatabaseIntegration
from .utilities import ProposalUtilities
from services.chroma.chroma_service import ChromaService
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from controllers.kontratar.ebuy_opps_controller import EBUYOppsController
from controllers.kontratar.opps_table_controller import OppsTableController
from database import get_customer_db, get_kontratar_db


class MultiAgentPipelineService:
    """
    Multi-agent pipeline service that replaces ProposalOutlineService.
    Uses the same interface and credentials but leverages the multi-agent system internally.
    """
    
    def __init__(
        self,
        embedding_api_url: str = "http://ai.kontratar.com:5000",
        llm_api_url: str = "http://ai.kontratar.com:11434",
    ):
        # Initialize services with same endpoints as legacy system
        self.embedding_api_url = embedding_api_url
        self.llm_api_url = llm_api_url
        
        # Initialize controllers (same as legacy)
        self.sam_service = OppsTableController()
        self.ebuy_service = EBUYOppsController()
        self.custom_service = CustomOpportunitiesController()
        
        # Initialize ChromaDB service
        self.chroma_service = ChromaService(embedding_api_url, None)
        
        # Initialize multi-agent service
        self.multiagent_service = MultiAgentProposalService()

        # Initialize database integration
        self.db_integration = MultiAgentDatabaseIntegration()

        logger.info("Multi-Agent Pipeline Service initialized")
    
    async def generate_draft(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        table_of_contents: List[Dict[str, Any]],
        tenant_metadata: str,
        client_short_name: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate proposal draft using multi-agent system.
        Maintains the same interface as ProposalOutlineService.generate_draft().
        
        Args:
            opportunity_id: The opportunity identifier
            tenant_id: The tenant identifier
            source: The opportunity source (sam, ebuy, custom)
            table_of_contents: The table of contents structure
            tenant_metadata: Tenant metadata string
            client_short_name: Client short name
            **kwargs: Additional parameters
            
        Returns:
            Dict containing the generated proposal draft in legacy format
        """
        
        try:
            logger.info(f"Starting multi-agent proposal generation for opportunity {opportunity_id}")
            
            # Prepare job instruction for multi-agent system
            job_instruction = self._prepare_job_instruction(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                source=source,
                table_of_contents=table_of_contents,
                tenant_metadata=tenant_metadata,
                client_short_name=client_short_name,
                **kwargs
            )
            
            # Generate proposal using multi-agent system
            multiagent_result = await self.multiagent_service.generate_proposal(
                job_instruction=json.dumps(job_instruction),
                job_submitted_by="multiagent_pipeline"
            )
            
            # Convert multi-agent result to legacy format
            legacy_format_result = await self._convert_to_legacy_format(
                multiagent_result=multiagent_result,
                table_of_contents=table_of_contents,
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                source=source
            )

            # Store the proposal data in the database (same format as legacy)
            await self._store_proposal_data(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                source=source,
                table_of_contents=table_of_contents,
                proposal_draft=legacy_format_result
            )

            logger.info(f"Multi-agent proposal generation completed for opportunity {opportunity_id}")
            return legacy_format_result
            
        except Exception as e:
            logger.error(f"Error in multi-agent proposal generation: {e}")
            # Re-raise the exception instead of using fallback
            raise
    
    def _prepare_job_instruction(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        table_of_contents: List[Dict[str, Any]],
        tenant_metadata: str,
        client_short_name: str,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Prepare job instruction for the multi-agent system.
        Converts legacy parameters to multi-agent format.
        """
        
        # Extract volumes from table of contents
        volumes = []
        for i, section in enumerate(table_of_contents, 1):
            volumes.append(i)
        
        job_instruction = {
            "opportunityId": opportunity_id,
            "tenantId": tenant_id,
            "clientShortName": client_short_name,
            "opportunityType": source,
            "is_rfp": True,
            "generatedVolumes": volumes,
            "sourceDocuments": [],
            "tableOfContents": table_of_contents,
            "tenantMetadata": tenant_metadata,
            "aiPersonalityId": kwargs.get("personality_id"),
            "profileId": kwargs.get("profile_id"),
            "additionalContext": kwargs.get("additional_context", {})
        }
        
        return job_instruction

    async def _store_proposal_data(
        self,
        opportunity_id: str,
        tenant_id: str,
        source: str,
        table_of_contents: List[Dict[str, Any]],
        proposal_draft: Dict[str, Any]
    ) -> bool:
        """
        Store proposal data in the database using the database integration service.
        """

        try:
            success = await self.db_integration.store_proposal_data(
                opportunity_id=opportunity_id,
                tenant_id=tenant_id,
                source=source,
                table_of_contents=table_of_contents,
                proposal_draft=proposal_draft,
                additional_data=proposal_draft.get("metadata", {})
            )

            if success:
                logger.info(f"Successfully stored proposal data for opportunity {opportunity_id}")
            else:
                logger.error(f"Failed to store proposal data for opportunity {opportunity_id}")

            return success

        except Exception as e:
            logger.error(f"Error storing proposal data for opportunity {opportunity_id}: {e}")
            return False

    async def _convert_to_legacy_format(
        self,
        multiagent_result: Dict[str, Any],
        table_of_contents: List[Dict[str, Any]],
        opportunity_id: str,
        tenant_id: str,
        source: str
    ) -> Dict[str, Any]:
        """
        Convert multi-agent result to legacy ProposalOutlineService format.
        Ensures compatibility with existing database storage and PDF export.
        """
        
        try:
            # Extract proposal state from multi-agent result
            proposal_state = multiagent_result.get("proposal_state", {})
            volumes = proposal_state.get("volumes", {})
            
            # Build legacy format draft
            draft_sections = []
            
            for i, toc_section in enumerate(table_of_contents):
                section_number = toc_section.get("number", str(i + 1))
                section_title = f"{section_number} {toc_section.get('title', 'Untitled Section')}"
                
                # Get content from multi-agent volumes
                section_content = self._extract_section_content(
                    volumes=volumes,
                    section_index=i,
                    section_title=section_title,
                    toc_section=toc_section
                )
                
                # Create main section
                draft_section = {
                    "title": section_title,
                    "content": section_content,
                    "number": section_number
                }
                
                # Add subsections if they exist
                if "subsections" in toc_section and toc_section["subsections"]:
                    draft_section["subsections"] = []
                    for subsection in toc_section["subsections"]:
                        subsection_content = self._extract_subsection_content(
                            volumes=volumes,
                            section_index=i,
                            subsection=subsection
                        )
                        
                        draft_subsection = {
                            "title": f"{subsection.get('number', '')} {subsection.get('title', 'Untitled Subsection')}",
                            "content": subsection_content,
                            "number": subsection.get("number", "")
                        }
                        draft_section["subsections"].append(draft_subsection)
                
                draft_sections.append(draft_section)
            
            # Return in legacy format
            return {
                "draft": draft_sections,
                "metadata": {
                    "opportunity_id": opportunity_id,
                    "tenant_id": tenant_id,
                    "source": source,
                    "generated_at": datetime.utcnow().isoformat(),
                    "generator": "multiagent_pipeline",
                    "quality_score": proposal_state.get("overall_quality_score", 0.0),
                    "compliance_score": proposal_state.get("overall_compliance_score", 0.0)
                }
            }
            
        except Exception as e:
            logger.error(f"Error converting multi-agent result to legacy format: {e}")
            # Re-raise the exception instead of using fallback
            raise
    
    def _extract_section_content(
        self,
        volumes: Dict[str, Any],
        section_index: int,
        section_title: str,
        toc_section: Dict[str, Any]
    ) -> str:
        """
        Extract section content from multi-agent volumes.
        """
        
        # Try to find content in volumes by section index or title
        for volume_key, volume_data in volumes.items():
            if isinstance(volume_data, dict) and "content" in volume_data:
                volume_content = volume_data["content"]
                
                # Look for section content by title or index
                if isinstance(volume_content, dict):
                    # Try exact title match
                    if section_title in volume_content:
                        return volume_content[section_title]
                    
                    # Try section number match
                    section_number = toc_section.get("number", str(section_index + 1))
                    for key, content in volume_content.items():
                        if section_number in key:
                            return content
                
                elif isinstance(volume_content, str) and section_index == 0:
                    # If it's a string and this is the first section, use it
                    return volume_content
        
        # No fallback - raise error if content not found
        raise ValueError(f"No content found for section: {section_title}")
    
    def _extract_subsection_content(
        self,
        volumes: Dict[str, Any],
        section_index: int,
        subsection: Dict[str, Any]
    ) -> str:
        """
        Extract subsection content from multi-agent volumes.
        """
        
        subsection_title = subsection.get("title", "")
        subsection_number = subsection.get("number", "")
        
        # Try to find content in volumes
        for volume_key, volume_data in volumes.items():
            if isinstance(volume_data, dict) and "content" in volume_data:
                volume_content = volume_data["content"]
                
                if isinstance(volume_content, dict):
                    # Try exact title match
                    if subsection_title in volume_content:
                        return volume_content[subsection_title]
                    
                    # Try number match
                    for key, content in volume_content.items():
                        if subsection_number in key:
                            return content
        
        # No fallback - raise error if content not found
        raise ValueError(f"No content found for subsection: {subsection_title}")
    

