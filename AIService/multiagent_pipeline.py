#!/usr/bin/env python3
"""
Multi-Agent Proposal Generation Pipeline.
Drop-in replacement for pipeline_4.py that uses the multi-agent system.
Maintains the same interface and credentials for seamless integration.
"""

import asyncio
import json
from controllers.customer.tenant_controller import TenantController
from services.proposal.utilities import ProposalUtilities
from services.proposal.multiagent_pipeline_service import MultiAgentPipelineService
from controllers.customer.custom_opps_controller import CustomOpportunitiesController
from database import get_customer_db


async def main():
    """
    Main function that replicates the pipeline_4.py workflow using multi-agent system.
    Uses the same test data and credentials for compatibility.
    """
    
    # Same test data as pipeline_4.py
    opportunity_id = "iRiYNgd8RC"
    tenant_id = "8d9e9729-f7bd-44a0-9cf1-777f532a2db2"
    client = "adeptengineeringsolutions"
    source = "custom"
    
    print(f"Starting multi-agent proposal generation for opportunity: {opportunity_id}")
    print(f"Tenant ID: {tenant_id}")
    print(f"Client: {client}")
    print(f"Source: {source}")
    
    # Initialize multi-agent pipeline service (replaces ProposalOutlineService)
    multiagent_service = MultiAgentPipelineService()
    custom_controller = CustomOpportunitiesController
    tenant_controller = TenantController
    
    # Get table of contents and tenant metadata (same as legacy)
    async for db in get_customer_db():
        record = await custom_controller.get_table_of_contents(db, opportunity_id)
        table_of_contents = json.loads(str(record[0])) if record and record[0] is not None else []
        tenant = await tenant_controller.get_by_tenant_id(db, tenant_id)
        tenant_metadata = f"{tenant}"
        break
    
    # Fallback to file if database doesn't have TOC (same as legacy)
    if not table_of_contents:
        print("No table of contents found in database, reading from file...")
        table_of_contents = ProposalUtilities.read_json_from_file("table-of-contents.json").get("table_of_contents", [])
    
    print(f"Table of contents loaded: {len(table_of_contents)} sections")
    
    # Generate proposal draft using multi-agent system
    print("Generating proposal draft using multi-agent system...")
    proposal_draft = await multiagent_service.generate_draft(
        opportunity_id=opportunity_id,
        tenant_id=tenant_id,
        source=source,
        table_of_contents=table_of_contents,
        tenant_metadata=tenant_metadata,
        client_short_name=client
    )
    
    # Save to file for inspection (same as legacy)
    print("Saving proposal draft to file...")
    ProposalUtilities.save_json_to_file(proposal_draft, "multiagent-proposal-draft.json")
    
    # Update database with generated content (same format as legacy)
    print("Updating database with generated content...")
    toc = {"table_of_contents": table_of_contents}
    
    async for db in get_customer_db():
        await custom_controller.update_by_opportunity_id(db, opportunity_id, {
            "toc_text": json.dumps(toc.get("table_of_contents", [])),
            "draft": json.dumps(proposal_draft.get("draft", []))
        })
        break
    
    print("Updated Draft in Database")
    
    # Print summary
    print("\n" + "="*60)
    print("MULTI-AGENT PROPOSAL GENERATION COMPLETED")
    print("="*60)
    print(f"Opportunity ID: {opportunity_id}")
    print(f"Sections Generated: {len(proposal_draft.get('draft', []))}")
    
    # Print quality metrics if available
    metadata = proposal_draft.get("metadata", {})
    if "quality_score" in metadata:
        print(f"Quality Score: {metadata['quality_score']:.2f}")
    if "compliance_score" in metadata:
        print(f"Compliance Score: {metadata['compliance_score']:.2f}")
    
    print(f"Generator: {metadata.get('generator', 'unknown')}")
    print(f"Generated At: {metadata.get('generated_at', 'unknown')}")
    print("\nFiles created:")
    print("- multiagent-proposal-draft.json")
    print("\nDatabase updated with:")
    print("- Table of contents (toc_text)")
    print("- Proposal draft (draft)")
    print("\nThe proposal is now ready for PDF export using existing endpoints.")
    print("="*60)


async def test_comparison():
    """
    Optional function to compare legacy vs multi-agent results.
    Can be called separately to validate the multi-agent system.
    """
    
    print("Running comparison test between legacy and multi-agent systems...")
    
    # Load both results for comparison
    try:
        legacy_draft = ProposalUtilities.read_json_from_file("proposal-draft-3.json")
        multiagent_draft = ProposalUtilities.read_json_from_file("multiagent-proposal-draft.json")
        
        print(f"Legacy draft sections: {len(legacy_draft.get('draft', []))}")
        print(f"Multi-agent draft sections: {len(multiagent_draft.get('draft', []))}")
        
        # Compare section structure
        legacy_sections = legacy_draft.get("draft", [])
        multiagent_sections = multiagent_draft.get("draft", [])
        
        print("\nSection comparison:")
        for i, (legacy_section, multiagent_section) in enumerate(zip(legacy_sections, multiagent_sections)):
            legacy_title = legacy_section.get("title", "Unknown")
            multiagent_title = multiagent_section.get("title", "Unknown")
            
            print(f"Section {i+1}:")
            print(f"  Legacy: {legacy_title}")
            print(f"  Multi-agent: {multiagent_title}")
            print(f"  Match: {'✓' if legacy_title == multiagent_title else '✗'}")
        
        print("\nComparison completed.")
        
    except FileNotFoundError as e:
        print(f"Comparison failed: {e}")
        print("Make sure both pipeline_4.py and multiagent_pipeline.py have been run.")


async def validate_database_storage():
    """
    Validate that the multi-agent system stores data in the correct database format.
    """
    
    print("Validating database storage format...")
    
    opportunity_id = "iRiYNgd8RC"
    custom_controller = CustomOpportunitiesController
    
    try:
        async for db in get_customer_db():
            record = await custom_controller.get_by_opportunity_id(db, opportunity_id)
            break
        
        if record:
            print("✓ Database record found")
            print(f"✓ TOC text present: {'Yes' if record.toc_text else 'No'}")
            print(f"✓ Draft present: {'Yes' if record.draft else 'No'}")
            
            # Validate JSON format
            if record.toc_text:
                try:
                    toc_data = json.loads(record.toc_text)
                    print(f"✓ TOC JSON valid: {len(toc_data)} items")
                except json.JSONDecodeError:
                    print("✗ TOC JSON invalid")
            
            if record.draft:
                try:
                    draft_data = json.loads(record.draft)
                    print(f"✓ Draft JSON valid: {len(draft_data)} sections")
                except json.JSONDecodeError:
                    print("✗ Draft JSON invalid")
            
            print("Database validation completed successfully.")
        else:
            print("✗ No database record found")
    
    except Exception as e:
        print(f"Database validation failed: {e}")


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "compare":
            asyncio.run(test_comparison())
        elif sys.argv[1] == "validate":
            asyncio.run(validate_database_storage())
        else:
            print("Usage:")
            print("  python multiagent_pipeline.py           # Run multi-agent proposal generation")
            print("  python multiagent_pipeline.py compare   # Compare legacy vs multi-agent results")
            print("  python multiagent_pipeline.py validate  # Validate database storage")
    else:
        asyncio.run(main())
