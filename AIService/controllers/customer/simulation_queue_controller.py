from datetime import datetime
from typing import List, Optional

from loguru import logger
from models.customer_models import SimulationQueue
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession


class SimulationQueueController:
    """Controller for simulation queue operations"""
    
    @staticmethod
    async def get_new_items(db: AsyncSession, limit: int = 10) -> List[SimulationQueue]:
        """Get new simulation queue items with status NEW"""
        try:
            query = select(SimulationQueue).where(
                SimulationQueue.status == "NEW"
            ).order_by(SimulationQueue.created_at.asc()).limit(limit)
            
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting new simulation queue items: {e}")
            return []
    
    @staticmethod
    async def update_status(db: AsyncSession, queue_id: int, status: str) -> bool:
        """Update simulation queue status"""
        try:
            query = update(SimulationQueue).where(
                SimulationQueue.id == queue_id
            ).values(status=status)
            
            result = await db.execute(query)
            await db.commit()
            
            logger.info(f"Updated simulation queue status for id {queue_id} to {status}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating simulation queue status: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def get_by_id(db: AsyncSession, queue_id: int) -> Optional[SimulationQueue]:
        """Get simulation queue item by ID"""
        try:
            query = select(SimulationQueue).where(SimulationQueue.id == queue_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting simulation queue item {queue_id}: {e}")
            return None
    
    @staticmethod
    async def get_by_job_id(db: AsyncSession, job_id: str) -> Optional[SimulationQueue]:
        """Get simulation queue item by job ID"""
        try:
            query = select(SimulationQueue).where(SimulationQueue.job_id == job_id)
            result = await db.execute(query)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"Error getting simulation queue item by job_id {job_id}: {e}")
            return None
    
    @staticmethod
    async def get_by_job_submitted_by(db: AsyncSession, job_submitted_by: int, limit: int = 100) -> List[SimulationQueue]:
        """Get simulation queue items by job submitted by user ID"""
        try:
            query = select(SimulationQueue).where(
                SimulationQueue.job_submitted_by == job_submitted_by
            ).order_by(SimulationQueue.created_at.desc()).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting simulation queue items for user {job_submitted_by}: {e}")
            return []
    
    @staticmethod
    async def get_by_status(db: AsyncSession, status: str, limit: int = 100) -> List[SimulationQueue]:
        """Get simulation queue items by status"""
        try:
            query = select(SimulationQueue).where(
                SimulationQueue.status == status
            ).order_by(SimulationQueue.created_at.desc()).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting simulation queue items with status {status}: {e}")
            return []
    
    @staticmethod
    async def get_all(db: AsyncSession, limit: int = 100) -> List[SimulationQueue]:
        """Get all simulation queue items"""
        try:
            query = select(SimulationQueue).order_by(SimulationQueue.created_at.desc()).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting all simulation queue items: {e}")
            return []
    
    @staticmethod
    async def add(
        db: AsyncSession,
        job_instruction: str,
        job_submitted_by: int,
        job_id: str,
        status: str = "NEW"
    ) -> Optional[SimulationQueue]:
        """Add new simulation queue item"""
        try:
            new_item = SimulationQueue(
                status=status,
                job_instruction=job_instruction,
                job_submitted_by=job_submitted_by,
                job_id=job_id,
                created_at=datetime.now(),
            )
            
            db.add(new_item)
            await db.commit()
            await db.refresh(new_item)
            
            logger.info(f"Created new simulation queue item {new_item.id}")
            return new_item
        except Exception as e:
            logger.error(f"Error creating simulation queue item: {e}")
            await db.rollback()
            return None
    
    @staticmethod
    async def update(
        db: AsyncSession,
        queue_id: int,
        status: str = None,
        job_instruction: str = None
    ) -> bool:
        """Update simulation queue item"""
        try:
            item = await SimulationQueueController.get_by_id(db, queue_id)
            if not item:
                return False
            
            if status:
                item.status = status
            if job_instruction:
                item.job_instruction = job_instruction
            
            await db.commit()
            logger.info(f"Updated simulation queue item {queue_id}")
            return True
        except Exception as e:
            logger.error(f"Error updating simulation queue item: {e}")
            await db.rollback()
            return False 