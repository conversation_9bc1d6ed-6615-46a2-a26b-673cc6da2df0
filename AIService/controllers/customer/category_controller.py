from typing import List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, text, update
from models.customer_models import Category
from loguru import logger

class CategoryController:
    @staticmethod
    async def get_top_k_by_embedding(db: AsyncSession, embedding: list, k: int = 10) -> List[Category]:
        try:
            query = select(Category).order_by(text("embedding <=> :embedding")).params(embedding=embedding).limit(k)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting top-k categories by embedding: {e}")
            return []

    @staticmethod
    async def get_without_description(db: AsyncSession, limit: int = 20) -> List[Category]:
        try:
            query = select(Category).where(
                (Category.category_description == None) | (Category.category_description == "")
            ).limit(limit)
            result = await db.execute(query)
            return result.scalars().all()
        except Exception as e:
            logger.error(f"Error getting categories without description: {e}")
            return []

    @staticmethod
    async def update_by_category_name(
        db: AsyncSession,
        category_name: str,
        category_description: Optional[str] = None,
        embedding: Optional[list] = None
    ) -> bool:
        try:
            update_values = {}
            if category_description is not None:
                update_values["category_description"] = category_description
            if embedding is not None:
                update_values["embedding"] = embedding
            if not update_values:
                return False
            query = update(Category).where(Category.category_name == category_name).values(**update_values)
            result = await db.execute(query)
            await db.commit()
            logger.info(f"Updated category {category_name} with {update_values}")
            return result.rowcount > 0
        except Exception as e:
            logger.error(f"Error updating category {category_name}: {e}")
            await db.rollback()
            return False