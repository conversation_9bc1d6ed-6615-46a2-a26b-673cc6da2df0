import asyncio
from controllers.customer.category_controller import Category<PERSON>ontroller
from database import get_customer_db
from utils.llm import KontratarLLM
from utils.embedding_model import KontratarEmbeddings
from loguru import logger

llm = KontratarLLM(api_url="http://ai.kontratar.com:8080")
embedding_model = KontratarEmbeddings("http://ai.kontratar.com:5000")

async def process_category(category, db):
    # Generate description from LLM
    
    logger.info(f"Processing category: {category.category_name}")
    
    system_prompt = "You are a helpful assistant that generates concise and clear descriptions for business categories. Your descriptions should be professional, informative, and suitable for business contexts."
    prompt = f"Generate a concise, clear description for the category: '{category.category_name}'."
    
    logger.info(f"Generating description for category: {category.category_name}")
    description = llm._call(system_prompt=system_prompt, user_prompt=prompt, max_tokens=100)
    if hasattr(description, "content"):
        description = description.content
    
    logger.info(f"Generated description: {description[:100]}...")
    
    # Generate embedding
    logger.info(f"Generating embedding for category: {category.category_name}")
    embedding = embedding_model.embed_query(description)
    logger.info(f"Generated embedding with {len(embedding)} dimensions")
    
    # Update record
    logger.info(f"Updating database record for category: {category.category_name}")
    success = await CategoryController.update_by_category_name(
        db, category.category_name, category_description=description, embedding=embedding
    )
    
    if success:
        logger.info(f"Successfully updated category: {category.category_name}")
    else:
        logger.error(f"Failed to update category: {category.category_name}")

async def main():
    import asyncio

    while True:
        async for db in get_customer_db():
            categories = await CategoryController.get_without_description(db, limit=20)
            if not categories:
                print("No more categories without description.")
                return
            await asyncio.gather(*(process_category(cat, db) for cat in categories))
        # Wait for 5 minutes before the next run
        await asyncio.sleep(300)

if __name__ == "__main__":
    asyncio.run(main())